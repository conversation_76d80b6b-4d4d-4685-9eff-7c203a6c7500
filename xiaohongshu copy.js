/**
 * 小红书API操作模块
 * 包含API接口调用和互动元素获取功能
 * 
 * 功能列表：
 * 1. 设备注册API
 * 2. 获取链接API
 * 3. 更新操作状态API
 * 4. 设备令牌管理
 * 5. 操作信息管理
 * 6. 互动元素获取
 * 
 * 作者: Claude
 * 日期: 2024-07-12
 */

// 引入DeviceOperation模块，用于设备操作 - 兼容打包环境
var DeviceOperation;
try {
    DeviceOperation = require('./DeviceOperation.js');
} catch (e) {
    try {
        DeviceOperation = require('DeviceOperation.js');
    } catch (e2) {
        console.error("xiaohongshu.js: DeviceOperation模块加载失败:", e.message);
        throw e;
    }
}

// 在文件开头添加一个标志，表示是否是首次运行
let 是否首次运行 = true;

// 添加模块加载日志
console.log("xiaohongshu.js 模块开始加载...");

/**
 * 本地配置
 * 从config.txt文件读取配置信息
 */
let 本地配置 = {};

/**
 * 读取配置文件
 * 支持相对路径和绝对路径
 *
 * @param {string} 配置文件路径 - 配置文件路径，默认为config.txt
 * @returns {Object} - 配置对象
 */
function 读取配置文件(配置文件路径 = "config.txt") {
    try {
        let 文件路径;

        // 判断是否为绝对路径
        if (配置文件路径.includes(":/") || 配置文件路径.startsWith("/")) {
            // 绝对路径
            文件路径 = 配置文件路径;
        } else {
            // 相对路径，基于脚本目录
            文件路径 = files.path(配置文件路径);
        }

        console.log("尝试读取配置文件: " + 文件路径);

        if (!files.exists(文件路径)) {
            console.error("配置文件不存在: " + 文件路径);
            return {};
        }

        let 配置内容 = files.read(文件路径);
        if (!配置内容) {
            console.error("配置文件为空或读取失败");
            return {};
        }

        let 配置对象 = {};
        let 行列表 = 配置内容.split('\n');

        for (let i = 0; i < 行列表.length; i++) {
            let 行 = 行列表[i].trim();

            // 跳过空行和注释行
            if (!行 || 行.startsWith('#')) {
                continue;
            }

            // 解析配置项 key=value
            let 等号位置 = 行.indexOf('=');
            if (等号位置 > 0) {
                let 键 = 行.substring(0, 等号位置).trim();
                let 值 = 行.substring(等号位置 + 1).trim();

                // 智能类型转换
                配置对象[键] = 转换配置值(值);

                // 移除配置读取的详细日志
            }
        }

        return 配置对象;
    } catch (e) {
        console.error("读取配置文件出错: " + e.message);
        return {};
    }
}

/**
 * 转换配置值为合适的数据类型
 * @param {string} 值 - 原始字符串值
 * @returns {any} - 转换后的值
 */
function 转换配置值(值) {
    // 去除首尾空格
    值 = 值.trim();

    // 布尔值转换
    if (值.toLowerCase() === 'true' || 值 === '1') {
        return true;
    }
    if (值.toLowerCase() === 'false' || 值 === '0') {
        return false;
    }

    // 数字转换
    if (!isNaN(值) && 值 !== '') {
        // 如果包含小数点，转换为浮点数
        if (值.includes('.')) {
            return parseFloat(值);
        } else {
            return parseInt(值);
        }
    }

    // 数组转换（逗号分隔）
    if (值.includes(',')) {
        return 值.split(',').map(item => 转换配置值(item.trim()));
    }

    // 字符串（移除引号）
    if ((值.startsWith('"') && 值.endsWith('"')) ||
        (值.startsWith("'") && 值.endsWith("'"))) {
        return 值.slice(1, -1);
    }

    return 值;
}

/**
 * 获取配置项
 *
 * @param {string} 键名 - 配置项键名
 * @param {any} 默认值 - 如果配置项不存在时的默认值
 * @returns {any} - 配置项值
 */
function 获取配置项(键名, 默认值) {
    let 配置值;

    // 使用统一的配置读取方法
    if (typeof global !== 'undefined' && typeof global.读取配置项 === 'function') {
        配置值 = global.读取配置项(键名, 默认值);
        console.log(`[Global] 获取配置项 ${键名}: ${配置值} (类型: ${typeof 配置值})`);
    } else {
        // 如果global方法不可用，使用本地配置管理
        配置值 = configManager.获取配置项(键名, 默认值);
        console.log(`[Local] 获取配置项 ${键名}: ${配置值} (类型: ${typeof 配置值})`);
    }

    // AutoJS专用：智能类型转换
    return 智能类型转换(键名, 配置值, 默认值);
}

/**
 * AutoJS专用：智能类型转换
 * 根据配置项名称自动转换为正确的数据类型
 */
function 智能类型转换(键名, 配置值, 默认值) {
    // 如果配置值为空，直接返回默认值
    if (配置值 === null || 配置值 === undefined) {
        console.log(`配置项 ${键名} 为空，使用默认值: ${默认值}`);
        return 默认值;
    }

    // 数字类型的配置项
    if (键名.includes("时间") || 键名.includes("数量") || 键名.includes("几次") || 键名.includes("延时") || 键名.includes("行号")) {
        return 转换为数字(键名, 配置值, 默认值);
    }

    // 布尔类型的配置项
    if (键名.includes("启用") || 键名.includes("开启") || 键名.includes("允许")) {
        return 转换为布尔值(键名, 配置值, 默认值);
    }

    // 字符串类型的配置项（路径等）
    if (键名.includes("路径") || 键名.includes("文件") || 键名.includes("目录")) {
        return 转换为字符串(键名, 配置值, 默认值);
    }

    // 其他情况直接返回
    return 配置值;
}

/**
 * 转换为数字类型
 */
function 转换为数字(键名, 配置值, 默认值) {
    // 如果已经是数字且有效，直接返回
    if (typeof 配置值 === 'number' && !isNaN(配置值)) {
        return 配置值;
    }

    // 尝试从字符串转换
    if (typeof 配置值 === 'string') {
        let 转换值 = parseInt(配置值);
        if (!isNaN(转换值) && 转换值 >= 0) {
            console.log(`配置项 ${键名} 字符串转数字: "${配置值}" -> ${转换值}`);
            return 转换值;
        }
    }

    // 尝试从布尔值转换
    if (typeof 配置值 === 'boolean') {
        let 转换值 = 配置值 ? 1 : 0;
        console.log(`配置项 ${键名} 布尔转数字: ${配置值} -> ${转换值}`);
        return 转换值;
    }

    console.warn(`配置项 ${键名} 无法转换为数字: ${配置值} (类型: ${typeof 配置值})，使用默认值: ${默认值}`);
    return typeof 默认值 === 'number' ? 默认值 : 0;
}

/**
 * 转换为布尔值类型
 */
function 转换为布尔值(键名, 配置值, 默认值) {
    // 如果已经是布尔值，直接返回
    if (typeof 配置值 === 'boolean') {
        return 配置值;
    }

    // 从字符串转换
    if (typeof 配置值 === 'string') {
        let 小写值 = 配置值.toLowerCase();
        if (小写值 === 'true' || 小写值 === '1' || 小写值 === 'yes' || 小写值 === 'on') {
            console.log(`配置项 ${键名} 字符串转布尔: "${配置值}" -> true`);
            return true;
        }
        if (小写值 === 'false' || 小写值 === '0' || 小写值 === 'no' || 小写值 === 'off') {
            console.log(`配置项 ${键名} 字符串转布尔: "${配置值}" -> false`);
            return false;
        }
    }

    // 从数字转换
    if (typeof 配置值 === 'number') {
        let 转换值 = 配置值 > 0;
        console.log(`配置项 ${键名} 数字转布尔: ${配置值} -> ${转换值}`);
        return 转换值;
    }

    console.warn(`配置项 ${键名} 无法转换为布尔值: ${配置值} (类型: ${typeof 配置值})，使用默认值: ${默认值}`);
    return typeof 默认值 === 'boolean' ? 默认值 : false;
}

/**
 * 转换为字符串类型
 */
function 转换为字符串(键名, 配置值, 默认值) {
    // 如果已经是字符串，直接返回
    if (typeof 配置值 === 'string') {
        return 配置值;
    }

    // 从其他类型转换
    if (配置值 !== null && 配置值 !== undefined) {
        let 转换值 = String(配置值);
        console.log(`配置项 ${键名} 转换为字符串: ${配置值} -> "${转换值}"`);
        return 转换值;
    }

    console.warn(`配置项 ${键名} 无法转换为字符串: ${配置值}，使用默认值: ${默认值}`);
    return typeof 默认值 === 'string' ? 默认值 : "";
}

/**
 * 更新配置文件 - 支持JSON格式
 *
 * @param {string} 键名 - 要更新的配置项键名
 * @param {any} 值 - 新的值
 * @param {string} 配置文件路径 - 配置文件路径，默认使用JSON配置文件
 */
function 更新配置文件(键名, 值, 配置文件路径 = null) {
    try {
        // 优先使用JSON配置文件
        let 文件路径 = 配置文件路径 || CONFIG_FILE;

        console.log(`更新配置文件: ${键名} = ${值}`);

        // 读取现有配置
        let 现有配置 = {};
        if (files.exists(文件路径)) {
            try {
                let 配置内容 = files.read(文件路径);
                if (配置内容) {
                    if (文件路径.endsWith('.json')) {
                        // JSON格式
                        现有配置 = JSON.parse(配置内容);
                    } else {
                        // 旧的key=value格式
                        现有配置 = 解析旧格式配置(配置内容);
                    }
                }
            } catch (e) {
                console.warn("读取现有配置失败，使用空配置: " + e.message);
                现有配置 = {};
            }
        } else {
            console.log("配置文件不存在，将创建新文件: " + 文件路径);
        }

        // 更新配置项
        现有配置[键名] = 值;

        // 保存配置
        if (文件路径.endsWith('.json')) {
            // 保存为JSON格式
            let 配置JSON = JSON.stringify(现有配置, null, 2);
            files.write(文件路径, 配置JSON);
        } else {
            // 保存为旧格式
            let 配置行 = [];
            for (let 键 in 现有配置) {
                配置行.push(`${键}=${现有配置[键]}`);
            }
            files.write(文件路径, 配置行.join('\n'));
        }

        // 同时更新内存中的配置
        本地配置[键名] = 值;

        console.log(`✅ 配置已更新并保存: ${键名} = ${值}`);
        return true;
    } catch (e) {
        console.error("更新配置文件出错: " + e.message);
        return false;
    }
}

/**
 * 解析旧格式配置文件（key=value格式）
 */
function 解析旧格式配置(配置内容) {
    let 配置对象 = {};
    let 行列表 = 配置内容.split('\n');

    for (let i = 0; i < 行列表.length; i++) {
        let 行 = 行列表[i].trim();

        // 跳过空行和注释行
        if (!行 || 行.startsWith('#')) {
            continue;
        }

        // 解析配置项 key=value
        let 等号位置 = 行.indexOf('=');
        if (等号位置 > 0) {
            let 键 = 行.substring(0, 等号位置).trim();
            let 值 = 行.substring(等号位置 + 1).trim();

            // 智能类型转换
            配置对象[键] = 转换配置值(值);
        }
    }

    return 配置对象;
}

/**
 * 读取URL文件
 * 支持相对路径和绝对路径
 *
 * @returns {Array} - URL列表
 */
function 读取URL文件() {
    try {
        let URL文件路径 = 获取配置项("链接文件路径", DEFAULT_URL_FILE);
        let 文件路径;

        // 判断是否为绝对路径
        if (URL文件路径.includes(":/") || URL文件路径.startsWith("/")) {
            // 绝对路径
            文件路径 = URL文件路径;
        } else {
            // 相对路径，基于脚本目录
            文件路径 = files.path(URL文件路径);
        }

        // 移除URL文件读取的详细日志

        if (!files.exists(文件路径)) {
            console.error("URL文件不存在: " + 文件路径);
            return [];
        }

        let 文件内容 = files.read(文件路径);
        if (!文件内容) {
            console.error("URL文件为空或读取失败");
            return [];
        }

        let URL列表 = [];
        let 行列表 = 文件内容.split('\n');

        for (let i = 0; i < 行列表.length; i++) {
            let 行 = 行列表[i].trim();

            // 跳过空行和注释行
            if (!行 || 行.startsWith('#')) {
                continue;
            }

            // 简单验证URL格式
            if (行.includes('xiaohongshu.com') || 行.includes('xhslink.com')) {
                URL列表.push(行);
                // 移除单个URL读取日志，避免输出过多
            } else {
                // 移除跳过URL的警告日志，避免输出过多
            }
        }

        // 只在URL数量为0时输出警告，正常情况下不输出
        if (URL列表.length === 0) {
            console.warn("未读取到任何有效URL");
        }
        return URL列表;
    } catch (e) {
        console.error("读取URL文件出错: " + e.message);
        return [];
    }
}

// 使用新的JSON配置文件
var CONFIG_FILE = "/sdcard/xiaohongshu_config.json";
var DEFAULT_URL_FILE = "/sdcard/xiaohongshu_url.txt";
var OPERATED_LINKS_FILE = "/sdcard/xiaohongshu_operated_links.json";

// 简单的配置管理
var configManager = {
    读取配置: function () {
        try {
            if (!files.exists(CONFIG_FILE)) {
                console.log("配置文件不存在，使用默认配置");
                return this.DEFAULT_CONFIG;
            }

            var 配置内容 = files.read(CONFIG_FILE);
            if (!配置内容) {
                console.log("配置文件为空，使用默认配置");
                return this.DEFAULT_CONFIG;
            }

            var 配置对象 = JSON.parse(配置内容);

            // 手动合并配置，避免使用Object.assign
            var 合并配置 = {};

            // 先复制默认配置
            for (var 键 in this.DEFAULT_CONFIG) {
                合并配置[键] = this.DEFAULT_CONFIG[键];
            }

            // 再覆盖用户配置
            for (var 键 in 配置对象) {
                合并配置[键] = 配置对象[键];
            }

            console.log("成功读取配置文件，配置项数量: " + Object.keys(合并配置).length);
            return 合并配置;

        } catch (e) {
            console.error("读取配置失败: " + e.message);
            return this.DEFAULT_CONFIG;
        }
    },

    DEFAULT_CONFIG: {
        操作换号几次改机: 4,
        链接文件路径: DEFAULT_URL_FILE,
        当前操作行号: 1,
        启用点赞: true,
        启用收藏: true,
        启用阅读: true,
        每个账号点赞数量: 3,
        每个账号收藏数量: 3,
        每个账号阅读数量: 3,
        阅读时间: 10000,
        跳转链接延时: 3000
    },

    获取配置项: function (键名, 默认值) {
        var 配置 = this.读取配置();
        return 配置.hasOwnProperty(键名) ? 配置[键名] : 默认值;
    }
};

console.log("使用新的JSON配置管理，配置文件: " + CONFIG_FILE);

// 检查AutoJS环境
if (typeof auto === 'undefined') {
    console.log("警告：未在AutoJS环境中运行，部分功能可能不可用");
    // 创建模拟device对象，避免报错
    if (typeof device === 'undefined') {
        global.device = {
            width: 1080,
            height: 1920,
            brand: "模拟",
            model: "设备",
            release: "未知"
        };
    }
}

console.log("小红书API模块已加载");

/**
 * 生成随机设备令牌
 * 
 * @returns {string} - 随机生成的设备令牌
 */
function 生成设备令牌() {
    // 使用与网页测试一致的格式：device_时间戳_随机字符串
    const 时间戳 = Date.now();
    let 随机字符串 = "";
    const 可能字符 = "abcdefghijklmnopqrstuvwxyz0123456789";

    // 生成8位随机字符串
    for (let i = 0; i < 8; i++) {
        随机字符串 += 可能字符.charAt(Math.floor(Math.random() * 可能字符.length));
    }

    // 格式: device_时间戳_随机字符串
    return `device_${时间戳}_${随机字符串}`;
}

/**
 * 本地操作计数器
 */
let 本地操作计数 = {
    当前点赞数: 0,
    当前收藏数: 0,
    当前阅读数: 0,
    当前URL索引: 0,
    URL文件总行数: 0,
    最后操作时间: null
};

/**
 * 获取下一个URL
 * 从本地URL列表中获取下一个要处理的URL，支持断点续传
 * 使用配置文件中的当前操作行号
 *
 * @returns {Object} - 包含URL和相关信息的对象
 */
function 获取下一个URL() {
    try {
        let URL列表 = 读取URL文件();
        if (!URL列表 || URL列表.length === 0) {
            return {
                success: false,
                status: "no_links",
                message: "没有可用的URL"
            };
        }

        // 从配置文件获取当前操作行号（从1开始）
        let 当前行号 = 获取配置项("当前操作行号", 1);

        // 确保行号在有效范围内
        if (当前行号 < 1) {
            当前行号 = 1;
        }

        // 如果超出范围，直接结束处理（不再循环）
        if (当前行号 > URL列表.length) {
            console.log(`当前行号 ${当前行号} 超出URL总数 ${URL列表.length}`);
            console.log("🎯 所有链接都已处理完毕，结束处理");

            // 显示最终操作统计
            let 启用点赞 = 获取配置项("启用点赞", false);
            let 启用收藏 = 获取配置项("启用收藏", false);
            let 启用阅读 = 获取配置项("启用阅读", false);

            let 每个账号点赞数量 = 获取配置项("每个账号点赞数量", 50);
            let 每个账号收藏数量 = 获取配置项("每个账号收藏数量", 30);
            let 每个账号阅读数量 = 获取配置项("每个账号阅读数量", 100);

            console.log("📊 最终操作统计:");
            console.log(`点赞: ${本地操作计数.当前点赞数}/${每个账号点赞数量} (启用: ${启用点赞})`);
            console.log(`收藏: ${本地操作计数.当前收藏数}/${每个账号收藏数量} (启用: ${启用收藏})`);
            console.log(`阅读: ${本地操作计数.当前阅读数}/${每个账号阅读数量} (启用: ${启用阅读})`);

            // 检查是否有操作达到了目标
            let 点赞已达上限 = 启用点赞 && 本地操作计数.当前点赞数 >= 每个账号点赞数量;
            let 收藏已达上限 = 启用收藏 && 本地操作计数.当前收藏数 >= 每个账号收藏数量;
            let 阅读已达上限 = 启用阅读 && 本地操作计数.当前阅读数 >= 每个账号阅读数量;

            if (点赞已达上限 || 收藏已达上限 || 阅读已达上限) {
                console.log("✅ 部分操作已达到目标数量");
                return {
                    success: false,
                    status: "limit_reached",
                    message: "部分操作已达到目标数量，所有链接处理完毕"
                };
            } else {
                console.log("📋 链接数量不足以完成所有目标操作");
                return {
                    success: false,
                    status: "insufficient_links",
                    message: "链接数量不足以完成所有目标操作"
                };
            }
        }

        // 检查是否已达到操作上限
        let 每个账号点赞数量 = 获取配置项("每个账号点赞数量", 50);
        let 每个账号收藏数量 = 获取配置项("每个账号收藏数量", 30);
        let 每个账号阅读数量 = 获取配置项("每个账号阅读数量", 100);

        let 启用点赞 = 获取配置项("启用点赞", false);
        let 启用收藏 = 获取配置项("启用收藏", false);
        let 启用阅读 = 获取配置项("启用阅读", false);

        // 检查是否所有启用的操作都已达到上限
        let 点赞已达上限 = !启用点赞 || 本地操作计数.当前点赞数 >= 每个账号点赞数量;
        let 收藏已达上限 = !启用收藏 || 本地操作计数.当前收藏数 >= 每个账号收藏数量;
        let 阅读已达上限 = !启用阅读 || 本地操作计数.当前阅读数 >= 每个账号阅读数量;

        if (点赞已达上限 && 收藏已达上限 && 阅读已达上限) {
            console.log("所有启用的操作都已达到上限");
            console.log(`点赞: ${本地操作计数.当前点赞数}/${每个账号点赞数量} (启用: ${启用点赞})`);
            console.log(`收藏: ${本地操作计数.当前收藏数}/${每个账号收藏数量} (启用: ${启用收藏})`);
            console.log(`阅读: ${本地操作计数.当前阅读数}/${每个账号阅读数量} (启用: ${启用阅读})`);
            return {
                success: false,
                status: "limit_reached",
                message: "所有启用的操作都已达到上限"
            };
        }

        // 获取当前URL（数组索引从0开始，所以行号-1）
        let URL索引 = 当前行号 - 1;
        let 当前URL = URL列表[URL索引];

        // 移除URL读取日志，避免过多输出

        // 注意：这里不再递增行号，只有操作完成后才递增
        // 同时更新本地计数（为了兼容性）
        本地操作计数.当前URL索引 = URL索引;
        本地操作计数.最后操作时间 = new Date().toISOString();
        保存本地操作计数();

        return {
            success: true,
            status: "success",
            data: {
                url: 当前URL,
                link_id: Date.now(), // 使用时间戳作为链接ID
                current_line: 当前行号,
                total_lines: URL列表.length,
                operation_count: 本地操作计数.当前点赞数 + 本地操作计数.当前收藏数 + 本地操作计数.当前阅读数
            }
        };
    } catch (e) {
        console.error("获取下一个URL出错: " + e.message);
        return {
            success: false,
            status: "error",
            message: "获取URL出错: " + e.message
        };
    }
}

/**
 * 递增当前操作行号
 * 只有在操作完成后才调用此函数
 */
function 递增当前操作行号() {
    try {
        let 当前行号 = 获取配置项("当前操作行号", 1);
        let 下一行号 = 当前行号 + 1;

        console.log(`递增操作行号: ${当前行号} -> ${下一行号}`);
        更新配置文件("当前操作行号", 下一行号);

        return 下一行号;
    } catch (e) {
        console.error("递增操作行号失败: " + e.message);
        return null;
    }
}

/**
 * 读取已操作链接列表
 * @returns {Array} - 已操作的链接列表
 */
function 读取已操作链接列表() {
    try {
        if (!files.exists(OPERATED_LINKS_FILE)) {
            return [];
        }

        let 文件内容 = files.read(OPERATED_LINKS_FILE);
        if (!文件内容) {
            return [];
        }

        return JSON.parse(文件内容);
    } catch (e) {
        console.error("读取已操作链接列表失败: " + e.message);
        return [];
    }
}

/**
 * 保存已操作链接列表
 * @param {Array} 链接列表 - 要保存的链接列表
 */
function 保存已操作链接列表(链接列表) {
    try {
        files.write(OPERATED_LINKS_FILE, JSON.stringify(链接列表, null, 2));
        console.log(`✅ 已操作链接列表已保存到: ${OPERATED_LINKS_FILE}`);
    } catch (e) {
        console.error("保存已操作链接列表失败: " + e.message);
    }
}

/**
 * 添加已操作链接
 * @param {string} 链接 - 要添加的链接
 * @param {string} 操作类型 - 操作类型
 * @param {boolean} 成功 - 是否成功
 */
function 添加已操作链接(链接, 操作类型, 成功) {
    try {
        let 已操作链接列表 = 读取已操作链接列表();

        // 检查是否已存在
        let 现有记录 = 已操作链接列表.find(item => item.链接 === 链接);

        if (现有记录) {
            // 更新现有记录
            现有记录.操作类型.push(操作类型);
            现有记录.最后操作时间 = new Date().toISOString();
            现有记录.操作次数++;
            console.log(`📝 更新已操作链接记录: ${链接} (${操作类型})`);
        } else {
            // 添加新记录
            已操作链接列表.push({
                链接: 链接,
                操作类型: [操作类型],
                首次操作时间: new Date().toISOString(),
                最后操作时间: new Date().toISOString(),
                操作次数: 1,
                成功: 成功
            });
            console.log(`📝 添加新的已操作链接记录: ${链接} (${操作类型})`);
        }

        保存已操作链接列表(已操作链接列表);
    } catch (e) {
        console.error("添加已操作链接失败: " + e.message);
    }
}

/**
 * 检查链接是否已操作过
 * @param {string} 链接 - 要检查的链接
 * @param {string} 操作类型 - 操作类型 (可选)
 * @returns {boolean} - 是否已操作过
 */
function 检查链接是否已操作(链接, 操作类型 = null) {
    try {
        let 已操作链接列表 = 读取已操作链接列表();
        let 记录 = 已操作链接列表.find(item => item.链接 === 链接);

        if (!记录) {
            return false;
        }

        if (操作类型) {
            return 记录.操作类型.includes(操作类型);
        }

        return true;
    } catch (e) {
        console.error("检查链接是否已操作失败: " + e.message);
        return false;
    }
}

/**
 * 设置飞行模式执行标记
 * 在执行飞行模式前调用，标记即将执行飞行模式
 */
function 设置飞行模式执行标记() {
    try {
        更新配置文件("飞行模式已执行", true);
        console.log("✅ 飞行模式执行标记已设置");
        return true;
    } catch (e) {
        console.error("设置飞行模式执行标记失败: " + e.message);
        return false;
    }
}

/**
 * 检查飞行模式执行标记
 * 脚本启动时调用，检查是否刚执行过飞行模式
 * @returns {boolean} - 是否刚执行过飞行模式
 */
function 检查飞行模式执行标记() {
    try {
        let 标记 = 获取配置项("飞行模式已执行", false);
        console.log(`🔍 飞行模式执行标记: ${标记}`);
        return 标记;
    } catch (e) {
        console.error("检查飞行模式执行标记失败: " + e.message);
        return false;
    }
}

/**
 * 清除飞行模式执行标记
 * 跳过飞行模式后调用，为下次循环准备
 */
function 清除飞行模式执行标记() {
    try {
        更新配置文件("飞行模式已执行", false);
        console.log("✅ 飞行模式执行标记已清除");
        return true;
    } catch (e) {
        console.error("清除飞行模式执行标记失败: " + e.message);
        return false;
    }
}

/**
 * 执行飞行模式操作
 * 封装的飞行模式执行函数，包含标记设置和脚本退出
 * @param {Object} flows模块 - flows模块对象
 */
function 执行飞行模式操作(flows模块) {
    try {
        console.log("🛫 准备执行飞行模式操作");

        // 1. 设置执行标记
        if (!设置飞行模式执行标记()) {
            console.error("❌ 设置飞行模式标记失败，取消执行");
            return false;
        }

        // 2. 执行飞行模式
        console.log("🛫 执行飞行模式...");
        flows模块.重启飞行模式();

        // 3. 退出脚本
        console.log("🔚 飞行模式执行完成，退出脚本");
        toast("飞行模式执行完成，脚本将退出", 2000);

        // 延迟退出，确保toast显示
        setTimeout(() => {
            exit();
        }, 2000);

        return true;
    } catch (e) {
        console.error("执行飞行模式操作失败: " + e.message);
        return false;
    }
}

/**
 * 处理飞行模式逻辑
 * 在main.js中调用，自动处理飞行模式的执行和跳过逻辑
 * @param {Object} flows模块 - flows模块对象
 * @returns {boolean} - 是否需要继续执行后续逻辑
 */
function 处理飞行模式逻辑(flows模块) {
    try {
        console.log("🔍 检查飞行模式执行状态");

        // 检查是否刚执行过飞行模式
        if (检查飞行模式执行标记()) {
            console.log("⏭️ 检测到飞行模式已执行，跳过此次飞行模式操作");
            toast("跳过飞行模式操作", 1500);

            // 清除标记，为下次循环准备
            清除飞行模式执行标记();

            // 返回true，继续执行后续逻辑
            return true;
        } else {
            console.log("🛫 需要执行飞行模式操作");

            // 执行飞行模式并退出脚本
            执行飞行模式操作(flows模块);

            // 返回false，因为脚本将退出
            return false;
        }
    } catch (e) {
        console.error("处理飞行模式逻辑失败: " + e.message);
        return true; // 出错时继续执行
    }
}

/**
 * 处理链接操作完成（成功或失败都要递增行号）
 * @param {string} 链接 - 当前处理的链接
 * @param {string} 操作类型 - 操作类型
 * @param {boolean} 成功 - 是否成功
 * @param {string} 错误信息 - 错误信息
 */
function 处理链接操作完成(链接, 操作类型, 成功, 错误信息) {
    // 记录操作日志
    记录操作日志(链接, 操作类型, 成功, 错误信息);

    // 添加到已操作链接列表
    if (成功) {
        添加已操作链接(链接, 操作类型, 成功);
    }

    // 递增行号（无论成功还是失败）
    console.log("处理链接完成，递增行号...");
    let 新行号 = 递增当前操作行号();
    if (新行号) {
        console.log(`✅ 行号已更新为: ${新行号}`);
    } else {
        console.log("❌ 行号更新失败");
    }
}

/**
 * 更新本地操作计数
 *
 * @param {string} 操作类型 - "like", "collect", "read"
 * @param {boolean} 成功 - 操作是否成功
 */
function 更新本地操作计数(操作类型, 成功 = true) {
    if (!成功) return;

    switch (操作类型) {
        case "like":
            本地操作计数.当前点赞数++;
            console.log(`点赞计数更新: ${本地操作计数.当前点赞数}/${获取配置项("每个账号点赞数量", 50)}`);
            break;
        case "collect":
            本地操作计数.当前收藏数++;
            console.log(`收藏计数更新: ${本地操作计数.当前收藏数}/${获取配置项("每个账号收藏数量", 30)}`);
            break;
        case "read":
            本地操作计数.当前阅读数++;
            console.log(`阅读计数更新: ${本地操作计数.当前阅读数}/${获取配置项("每个账号阅读数量", 100)}`);
            break;
        case "both":
            本地操作计数.当前点赞数++;
            本地操作计数.当前收藏数++;
            console.log(`点赞计数更新: ${本地操作计数.当前点赞数}/${获取配置项("每个账号点赞数量", 50)}`);
            console.log(`收藏计数更新: ${本地操作计数.当前收藏数}/${获取配置项("每个账号收藏数量", 30)}`);
            break;
    }

    // 保存到本地存储
    storages.create("小红书操作").put("本地操作计数", 本地操作计数);
}

/**
 * 保存本地操作计数
 */
function 保存本地操作计数() {
    storages.create("小红书操作").put("本地操作计数", 本地操作计数);
}

/**
 * 加载本地操作计数
 */
function 加载本地操作计数() {
    let 存储的计数 = storages.create("小红书操作").get("本地操作计数");
    if (存储的计数) {
        // 确保新字段存在
        本地操作计数 = Object.assign({
            当前点赞数: 0,
            当前收藏数: 0,
            当前阅读数: 0,
            当前URL索引: 0,
            URL文件总行数: 0,
            最后操作时间: null
        }, 存储的计数);

        console.log("加载本地操作计数:", JSON.stringify(本地操作计数));
        console.log(`上次操作到第 ${本地操作计数.当前URL索引} 行，最后操作时间: ${本地操作计数.最后操作时间}`);
    } else {
        console.log("使用默认操作计数");
    }
}

/**
 * 重置本地操作计数
 *
 * @param {boolean} 重置URL索引 - 是否重置URL索引，默认true
 */
function 重置本地操作计数(重置URL索引 = true) {
    if (重置URL索引) {
        本地操作计数 = {
            当前点赞数: 0,
            当前收藏数: 0,
            当前阅读数: 0,
            当前URL索引: 0,
            URL文件总行数: 0,
            最后操作时间: null
        };
        console.log("已重置本地操作计数（包括URL索引）");
    } else {
        // 只重置操作计数，保留URL索引
        本地操作计数.当前点赞数 = 0;
        本地操作计数.当前收藏数 = 0;
        本地操作计数.当前阅读数 = 0;
        本地操作计数.最后操作时间 = null;
        console.log("已重置操作计数（保留URL索引）");
    }
    保存本地操作计数();
}

/**
 * 显示当前进度
 */
function 显示当前进度() {
    let 当前行号 = 获取配置项("当前操作行号", 1);
    let 点赞上限 = 获取配置项("每个账号点赞数量", 50);
    let 收藏上限 = 获取配置项("每个账号收藏数量", 30);
    let 阅读上限 = 获取配置项("每个账号阅读数量", 100);

    // 获取URL总数
    let URL列表 = 读取URL文件();
    let URL总数 = URL列表 ? URL列表.length : 0;

    console.log("=== 当前进度 ===");
    console.log(`URL进度: 第${当前行号}行/${URL总数}行`);
    console.log(`点赞进度: ${本地操作计数.当前点赞数}/${点赞上限}`);
    console.log(`收藏进度: ${本地操作计数.当前收藏数}/${收藏上限}`);
    console.log(`阅读进度: ${本地操作计数.当前阅读数}/${阅读上限}`);
    if (本地操作计数.最后操作时间) {
        console.log(`最后操作: ${本地操作计数.最后操作时间}`);
    }
    console.log("===============");
}



/**
 * 比较页面内容，判断是否为同一篇文章
 * @param {Object} 页面1 - 第一个页面的内容
 * @param {Object} 页面2 - 第二个页面的内容
 * @returns {boolean} 是否为同一篇文章
 */
function 比较页面内容(页面1, 页面2) {
    try {
        console.log("比较页面内容...");

        // 如果任一页面内容为空，返回false
        if (!页面1 || !页面2) {
            console.log("页面内容为空，判断为不同文章");
            return false;
        }

        // 比较标题
        if (页面1.标题 && 页面2.标题) {
            let 标题相似 = 页面1.标题.trim() === 页面2.标题.trim();
            console.log(`标题比较: "${页面1.标题}" vs "${页面2.标题}" - ${标题相似 ? "相同" : "不同"}`);
            return 标题相似;
        }

        // 如果没有标题，比较内容
        if (页面1.内容 && 页面2.内容) {
            // 取前100个字符进行比较
            let 内容1 = 页面1.内容.substring(0, 100).trim();
            let 内容2 = 页面2.内容.substring(0, 100).trim();
            let 内容相似 = 内容1 === 内容2;
            console.log(`内容比较: "${内容1}" vs "${内容2}" - ${内容相似 ? "相同" : "不同"}`);
            return 内容相似;
        }

        console.log("无法比较页面内容，判断为不同文章");
        return false;
    } catch (e) {
        console.error("比较页面内容出错: " + e.message);
        return false;
    }
}

/**
 * 创建暂停文件
 * 用于手动暂停脚本
 */
function 创建暂停文件() {
    let 暂停文件路径 = files.path("pause.txt");
    files.write(暂停文件路径, "删除此文件可继续脚本执行\n创建时间: " + new Date().toLocaleString());
    console.log("已创建暂停文件: " + 暂停文件路径);
    toast("脚本已暂停");
}

/**
 * 删除暂停文件
 * 用于恢复脚本执行
 */
function 删除暂停文件() {
    let 暂停文件路径 = files.path("pause.txt");
    if (files.exists(暂停文件路径)) {
        files.remove(暂停文件路径);
        console.log("已删除暂停文件，脚本将继续执行");
        toast("脚本继续执行");
        return true;
    }
    return false;
}

/**
 * 随机等待
 * 根据配置的时间范围进行随机等待，支持暂停检查
 *
 * @param {string} 等待类型 - "点击间隔", "操作间隔", "页面加载", "阅读"
 */
function 随机等待(等待类型 = "跳转链接延时") {
    let 等待时间;
    let 默认等待时间;

    // 设置默认值
    switch (等待类型) {
        case "跳转链接延时":
            默认等待时间 = 3000;
            等待时间 = 获取配置项("跳转链接延时", 默认等待时间);
            break;
        case "阅读":
            默认等待时间 = 10000;
            等待时间 = 获取配置项("阅读时间", 默认等待时间);
            break;
        case "操作间隔":
            默认等待时间 = 1500; // 操作间隔默认1.5秒
            等待时间 = 默认等待时间 + Math.random() * 1000; // 1.5-2.5秒随机
            break;
        case "点击":
            默认等待时间 = 500; // 点击后等待0.5秒
            等待时间 = 默认等待时间 + Math.random() * 500; // 0.5-1秒随机
            break;
        default:
            默认等待时间 = 3000;
            等待时间 = 默认等待时间;
    }

    // 由于有了智能类型转换，这里只需要简单验证
    if (typeof 等待时间 !== 'number' || isNaN(等待时间) || 等待时间 <= 0) {
        console.warn(`等待时间异常: ${等待时间}，使用默认值: ${默认等待时间}`);
        等待时间 = 默认等待时间;
    }

    console.log(`随机等待 ${等待类型}: ${等待时间} 毫秒`);
    sleep(等待时间);
}


/**
 * 检查是否需要换号
 *
 * @returns {boolean} - 是否需要换号
 */
function 检查是否需要换号() {
    if (!获取配置项("启用自动换号", true)) {
        console.log("自动换号功能已关闭");
        return false;
    }

    let 当前换号次数 = 获取配置项("当前换号次数", 0);
    let 最大换号次数 = 获取配置项("最大换号次数", 5);

    if (当前换号次数 >= 最大换号次数) {
        console.log(`已达到最大换号次数 ${最大换号次数}，停止脚本`);
        return false;
    }

    return true;
}

/**
 * 执行换号操作
 *
 * @returns {boolean} - 换号是否成功
 */
function 执行换号操作() {
    try {
        let 当前换号次数 = 获取配置项("当前换号次数", 0);
        let 最大换号次数 = 获取配置项("最大换号次数", 5);

        console.log(`开始第 ${当前换号次数 + 1} 次换号（最大 ${最大换号次数} 次）`);
        toast(`开始第 ${当前换号次数 + 1} 次换号`);

        // 更新换号次数
        当前换号次数++;
        更新配置文件("当前换号次数", 当前换号次数);

        // 这里可以添加具体的换号逻辑
        // 比如：清除应用数据、重新登录等
        console.log("换号操作完成");
        toast("换号操作完成");

        return true;
    } catch (e) {
        console.error("换号操作失败: " + e.message);
        return false;
    }
}

/**
 * 重置换号计数
 */
function 重置换号计数() {
    更新配置文件("当前换号次数", 0);
    console.log("已重置换号计数");
}

/**
 * 记录操作日志
 *
 * @param {string} 链接 - 操作的链接
 * @param {string} 操作类型 - 操作类型
 * @param {boolean} 成功 - 是否成功
 * @param {string} 错误信息 - 错误信息（如果有）
 */
function 记录操作日志(链接, 操作类型, 成功, 错误信息 = null) {
    let 日志信息 = {
        时间: new Date().toLocaleString(),
        链接: 链接,
        操作类型: 操作类型,
        结果: 成功 ? "成功" : "失败",
        错误信息: 错误信息
    };

    console.log(`操作日志: ${JSON.stringify(日志信息)}`);

    // 如果启用日志文件，保存到文件
    if (获取配置项("启用日志文件", true)) {
        try {
            // 修复：使用绝对路径，确保保存到手机根目录
            let 日志文件路径 = 获取配置项("日志文件路径", "/sdcard/xiaohongshu_logs/");
            let 日志文件名 = `${日志文件路径}operation_${new Date().toISOString().split('T')[0]}.log`;

            // 确保日志目录存在
            if (!files.exists(日志文件路径)) {
                files.createWithDirs(日志文件路径);
                console.log(`创建日志目录: ${日志文件路径}`);
            }

            // 增强日志内容，包含链接信息
            let 日志内容 = `${日志信息.时间} | ${日志信息.操作类型} | ${日志信息.结果} | ${日志信息.链接}`;
            if (错误信息) {
                日志内容 += ` | 错误: ${错误信息}`;
            }
            日志内容 += `\n`;

            files.append(日志文件名, 日志内容);
            console.log(`✅ 操作日志已保存到: ${日志文件名}`);
        } catch (e) {
            console.error("写入日志文件失败: " + e.message);
        }
    }
}


/**
 * 从文本中提取数字
 * 优化版本：提高提取效率，减少日志输出
 * 
 * @param {string} text - 包含数字的文本
 * @returns {number|null} - 提取的数字，失败返回null
 */
function 提取数字(text) {
    if (!text) return null;

    try {
        // 先移除所有空格，确保能处理"点赞 1445"这种格式
        let cleanText = text.replace(/\s+/g, "");

        // 匹配数字部分（包括小数和千分位）
        let 数字匹配 = cleanText.match(/\d+(\.\d+)?/);
        if (数字匹配) {
            // 转换为数字
            let 提取结果 = parseFloat(数字匹配[0]);
            return 提取结果;
        }

        // 如果是纯数字文本，直接解析
        if (/^\d+$/.test(text)) {
            let 提取结果 = parseInt(text);
            return 提取结果;
        }

        return null;
    } catch (e) {
        return null;
    }
}

// 判断当前是否为ROOT模式
function 当前为ROOT模式() {
    return DeviceOperation.获取交互操作模式() === 3;
}
// 无障碍模式已被禁用
function 当前为无障碍模式() {
    return false; // 无障碍模式已被禁用
}

// 获取互动元素（自动分发）
function 获取互动元素() {
    try {
        // ROOT模式专用
        if (当前为ROOT模式()) {
            console.log("ROOT模式下获取互动元素");
            return 使用ROOT获取互动元素();
        } else {
            console.error("脚本已配置为仅支持ROOT模式");
            return null;
        }
    } catch (e) {
        console.error("获取互动元素出错: " + e.message);
        return null;
    }
}

/**
 * 使用ROOT模式获取互动元素
 * 
 * @returns {Object|null} - 互动元素信息
 */
function 使用ROOT获取互动元素() {
    try {
        // 获取当前窗口XML
        let pageXml = DeviceOperation.获取窗口XML();
        if (!pageXml) {
            console.error("获取页面XML失败");
            return null;
        }

        console.log("成功获取窗口XML，开始解析互动元素");

        // 使用DeviceOperation的解析XML互动元素方法
        let 解析结果 = DeviceOperation.解析XML互动元素(pageXml);
        if (!解析结果) {
            console.log("解析XML互动元素失败");
            return null;
        }

        console.log("解析结果:", JSON.stringify(解析结果));

        // 构建返回结果
        let 结果 = {
            点赞: 解析结果.点赞 ? 解析结果.点赞.数量 : null,
            收藏: 解析结果.收藏 ? 解析结果.收藏.数量 : null,
            评论: 解析结果.评论 ? 解析结果.评论.数量 : null,
            点赞元素: null,
            收藏元素: null,
            评论元素: null,
            是否视频: 解析结果.内容类型 === "视频"
        };

        // 创建点赞元素对象
        if (解析结果.点赞 && 解析结果.点赞.坐标) {
            结果.点赞元素 = {
                bounds: () => ({
                    centerX: () => 解析结果.点赞.坐标.x,
                    centerY: () => 解析结果.点赞.坐标.y
                }),
                desc: () => `点赞 ${解析结果.点赞.数量 || 0}`,
                点击坐标: {
                    x: 解析结果.点赞.坐标.x,
                    y: 解析结果.点赞.坐标.y
                }
            };
            console.log(`找到点赞元素: (${解析结果.点赞.坐标.x}, ${解析结果.点赞.坐标.y}), 数量: ${解析结果.点赞.数量}`);
        }

        // 创建收藏元素对象
        if (解析结果.收藏 && 解析结果.收藏.坐标) {
            结果.收藏元素 = {
                bounds: () => ({
                    centerX: () => 解析结果.收藏.坐标.x,
                    centerY: () => 解析结果.收藏.坐标.y
                }),
                desc: () => `收藏 ${解析结果.收藏.数量 || 0}`,
                点击坐标: {
                    x: 解析结果.收藏.坐标.x,
                    y: 解析结果.收藏.坐标.y
                }
            };
            console.log(`找到收藏元素: (${解析结果.收藏.坐标.x}, ${解析结果.收藏.坐标.y}), 数量: ${解析结果.收藏.数量}`);
        }

        // 创建评论元素对象
        if (解析结果.评论 && 解析结果.评论.坐标) {
            结果.评论元素 = {
                bounds: () => ({
                    centerX: () => 解析结果.评论.坐标.x,
                    centerY: () => 解析结果.评论.坐标.y
                }),
                desc: () => `评论 ${解析结果.评论.数量 || 0}`,
                点击坐标: {
                    x: 解析结果.评论.坐标.x,
                    y: 解析结果.评论.坐标.y
                }
            };
            console.log(`找到评论元素: (${解析结果.评论.坐标.x}, ${解析结果.评论.坐标.y}), 数量: ${解析结果.评论.数量}`);
        }

        console.log(`内容类型: ${解析结果.内容类型}, 是否视频: ${结果.是否视频}`);

        if (结果.点赞元素 || 结果.收藏元素 || 结果.评论元素) {
            return 结果;
        }

        console.log("未找到任何互动元素");
        return null;
    } catch (e) {
        console.error("使用ROOT获取互动元素出错: " + e.message);
        return null;
    }
}

/**
 * 使用无障碍服务获取互动元素 (已禁用)
 *
 * @returns {Object|null} - 互动元素信息
 */
function 使用无障碍获取互动元素() {
    console.error("❌ 无障碍功能已被禁用");
    return null;
}

/**
 * 比较两个页面信息，判断是否为同一篇文章
 * 优化版本：提取最长的3段文本进行比较，不区分标题和内容
 * 
 * @param {Object} 信息1 - 第一个页面信息
 * @param {Object} 信息2 - 第二个页面信息
 * @returns {boolean} - 是否为同一篇文章
 */
function 比较页面信息(信息1, 信息2) {
    // 如果任一信息为空，返回false
    if (!信息1 || !信息2) {
        console.log("[比对] 有页面信息为空，直接判定不一致");
        return false;
    }

    // 确保最长文本数组存在
    信息1.最长文本列表 = 信息1.最长文本列表 || [];
    信息2.最长文本列表 = 信息2.最长文本列表 || [];

    // 输出详细调试信息
    console.log("[比对] 用户名1:", 信息1.用户名 || "null", "用户名2:", 信息2.用户名 || "null");

    // 输出最长文本列表
    console.log("━━━━━━━━━━ 文章1最长文本 ━━━━━━━━━━");
    for (let i = 0; i < 信息1.最长文本列表.length; i++) {
        let 文本 = 信息1.最长文本列表[i] || "";
        let 显示文本 = 文本.length > 100 ? 文本.substring(0, 100) + "..." : 文本;
        console.log(`[长文本1-${i + 1}] (${文本.length}字): ${显示文本}`);
    }

    console.log("━━━━━━━━━━ 文章2最长文本 ━━━━━━━━━━");
    for (let i = 0; i < 信息2.最长文本列表.length; i++) {
        let 文本 = 信息2.最长文本列表[i] || "";
        let 显示文本 = 文本.length > 100 ? 文本.substring(0, 100) + "..." : 文本;
        console.log(`[长文本2-${i + 1}] (${文本.length}字): ${显示文本}`);
    }

    // 1. 检查作者是否相同
    let 作者相同 = 信息1.用户名 && 信息2.用户名 && 信息1.用户名 === 信息2.用户名;
    if (!作者相同) {
        console.log("[比对] 用户名不一致");
    }

    // 2. 检查最长文本是否有相似项
    let 有相似文本 = false;
    let 相似文本数量 = 0;
    let 相似文本列表 = [];

    // 如果两篇文章都有最长文本列表，检查它们是否有相同/相似项
    if (信息1.最长文本列表.length > 0 && 信息2.最长文本列表.length > 0) {
        // 遍历第一篇文章的最长文本
        for (let i = 0; i < 信息1.最长文本列表.length; i++) {
            let 文本1 = 信息1.最长文本列表[i];
            if (!文本1 || 文本1.length < 5) continue;

            // 针对每个文本1，检查是否与文本2中的任何一项相似
            for (let j = 0; j < 信息2.最长文本列表.length; j++) {
                let 文本2 = 信息2.最长文本列表[j];
                if (!文本2 || 文本2.length < 5) continue;

                // 尝试多种比较方法
                // 1. 直接比较前30个字符
                const 比较长度1 = Math.min(30, 文本1.length, 文本2.length);
                const 文本1前缀1 = 文本1.substring(0, 比较长度1);
                const 文本2前缀1 = 文本2.substring(0, 比较长度1);

                // 2. 比较中间部分（避免开头不同但内容相同的情况）
                const 比较长度2 = Math.min(30, 文本1.length - 30, 文本2.length - 30);
                const 文本1中间 = 比较长度2 > 0 ? 文本1.substring(30, 60) : "";
                const 文本2中间 = 比较长度2 > 0 ? 文本2.substring(30, 60) : "";

                // 3. 比较结尾部分
                const 比较长度3 = Math.min(30, 文本1.length, 文本2.length);
                const 文本1结尾 = 文本1.length > 比较长度3 ? 文本1.substring(文本1.length - 比较长度3) : 文本1;
                const 文本2结尾 = 文本2.length > 比较长度3 ? 文本2.substring(文本2.length - 比较长度3) : 文本2;

                // 任意一种方式匹配即算相似
                if (文本1前缀1 === 文本2前缀1 ||
                    (文本1中间.length > 0 && 文本1中间 === 文本2中间) ||
                    文本1结尾 === 文本2结尾) {

                    相似文本数量++;
                    相似文本列表.push({
                        匹配方式: 文本1前缀1 === 文本2前缀1 ? "前缀匹配" :
                            (文本1中间 === 文本2中间 ? "中间部分匹配" : "结尾匹配"),
                        前缀1: 文本1前缀1,
                        前缀2: 文本2前缀1,
                        文本1: 文本1.substring(0, Math.min(200, 文本1.length)),
                        文本2: 文本2.substring(0, Math.min(200, 文本2.length))
                    });
                    console.log(`[比对] 找到相似文本 #${相似文本数量}: 匹配方式="${相似文本列表[相似文本数量 - 1].匹配方式}"`);
                    break; // 找到一个相似项就继续检查下一个文本1
                }
            }
        }

        // 如果找到至少一个相似文本，认为有相似性
        有相似文本 = 相似文本数量 > 0;
    }

    if (!有相似文本) {
        console.log("[比对] 没有找到相似的文本内容");
    } else {
        console.log(`[比对] 共找到 ${相似文本数量} 个相似文本`);
        for (let i = 0; i < 相似文本列表.length; i++) {
            console.log(`  - 相似文本${i + 1} (${相似文本列表[i].匹配方式}):`);
            console.log(`    文本1: ${相似文本列表[i].文本1}`);
            console.log(`    文本2: ${相似文本列表[i].文本2}`);
        }
    }

    // 3. 综合判断是否是同一篇文章
    // 如果用户名相同且有相似文本，或者相似文本数量>=2，认为是同一篇文章
    let 最终结果 = (作者相同 && 有相似文本) || 相似文本数量 >= 1;
    console.log(`[比对] 最终判定: ${最终结果 ? "一致" : "不一致"}`);
    return 最终结果;
}

/**
 * 显示性能优化总结
 * 在脚本启动时显示性能优化信息
 */
function 显示性能优化总结() {
    console.log("========== 小红书自动点赞脚本性能优化总结 ==========");
    console.log("1. 元素查找优化");
    console.log("   - 使用更精确的选择器，减少查找次数");
    console.log("   - 限制元素查找范围，只在可能区域内查找");
    console.log("   - 使用findOne替代find，提高查询效率");
    console.log("   - 优先使用descMatches而非遍历所有元素");

    console.log("2. 页面信息获取优化");
    console.log("   - 一次性获取所有文本元素，避免多次查询");
    console.log("   - 使用缓存避免重复获取相同信息");
    console.log("   - 优化文本过滤算法，提高筛选效率");
    console.log("   - 区分视频页面和图文页面的处理逻辑");

    console.log("3. 日志输出优化");
    console.log("   - 减少不必要的日志输出，降低I/O开销");
    console.log("   - 仅保留关键信息的输出，提高执行效率");

    console.log("4. 异常处理优化");
    console.log("   - 简化try-catch结构，减少嵌套层级");
    console.log("   - 统一错误处理逻辑，提高代码稳定性");

    console.log("5. 页面比较优化");
    console.log("   - 使用更高效的页面信息比较算法");
    console.log("   - 优先比较关键特征，快速判断页面相似性");

    console.log("=================================================");
}

// 在模块加载时显示优化信息
// 显示性能优化总结();

/**
 * 执行点赞操作
 * 
 * @returns {Object} - 点赞结果
 */
function 执行点赞(页面信息 = null) {
    console.log("执行点赞操作");

    try {
        // 优先使用精确坐标点击
        if (页面信息 && 页面信息.点赞按钮坐标) {
            console.log("使用精确坐标执行点赞操作");
            let 坐标 = 页面信息.点赞按钮坐标;

            console.log(`点赞按钮坐标: 中心点(${坐标.中心X}, ${坐标.中心Y})`);

            // 在点赞按钮中心点附近随机偏移5像素，模拟真实点击
            let 点击X = 坐标.中心X + (Math.random() * 10 - 5);
            let 点击Y = 坐标.中心Y + (Math.random() * 10 - 5);

            console.log(`实际点击坐标: (${Math.round(点击X)}, ${Math.round(点击Y)})`);

            // 执行精确点击
            DeviceOperation.点击(点击X, 点击Y);

            // 等待操作完成
            随机等待("点击");

            console.log("精确点赞点击完成");
            return { 成功: true, 信息: "使用精确坐标点赞成功" };
        }

        // 如果没有坐标信息，获取当前页面信息
        if (!页面信息) {
            console.log("未提供页面信息，尝试获取当前页面信息");
            页面信息 = 获取页面信息();

            if (页面信息 && 页面信息.点赞按钮坐标) {
                console.log("获取到点赞按钮坐标，使用精确点击");
                let 坐标 = 页面信息.点赞按钮坐标;

                let 点击X = 坐标.中心X + (Math.random() * 10 - 5);
                let 点击Y = 坐标.中心Y + (Math.random() * 10 - 5);

                console.log(`点赞按钮精确点击: (${Math.round(点击X)}, ${Math.round(点击Y)})`);
                DeviceOperation.点击(点击X, 点击Y);
                随机等待("点击");

                return { 成功: true, 信息: "使用获取的精确坐标点赞成功" };
            }
        }

        // 备用方案：使用随机点击策略
        console.log("⚠️ 未找到点赞按钮坐标，使用备用随机点击策略");

        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;

        // 屏幕中间位置
        let 中心X = 屏幕宽度 / 2;
        let 中心Y = 屏幕高度 / 2;

        // 生成随机点击次数（3-6次）
        let 点击次数 = Math.floor(Math.random() * 4) + 1;
        console.log(`将在屏幕中间区域随机点击 ${点击次数} 次`);

        // 第一次点击位置 - 在中心点周围随机100像素范围内生成坐标
        let 当前X = 中心X + (Math.random() * 200 - 100);
        let 当前Y = 中心Y + (Math.random() * 200 - 100);

        // 确保坐标在屏幕内
        当前X = Math.max(10, Math.min(屏幕宽度 - 10, 当前X));
        当前Y = Math.max(10, Math.min(屏幕高度 - 10, 当前Y));

        // 执行随机点击
        for (let i = 0; i < 点击次数; i++) {
            // 后续点击在前一次位置基础上偏移10像素内
            if (i > 0) {
                当前X += (Math.random() * 20 - 10); // ±10像素偏移
                当前Y += (Math.random() * 20 - 10); // ±10像素偏移

                // 确保坐标在屏幕内
                当前X = Math.max(10, Math.min(屏幕宽度 - 10, 当前X));
                当前Y = Math.max(10, Math.min(屏幕高度 - 10, 当前Y));
            }

            //console.log(`随机点击 #${i + 1}: (${Math.floor(当前X)}, ${Math.floor(当前Y)})`);

            // 直接使用DeviceOperation.点击，不需要再判断模式
            DeviceOperation.点击(当前X, 当前Y);

            // 随机等待20-50毫秒
            let min = 8;
            let max = 16;
            let 随机等待时间 = Math.floor(Math.random() * (max - min + 1)) + min;
            sleep(随机等待时间);
        }

        // 最后等待500毫秒
        //等待(500);

        return { 成功: true, 信息: `随机点击策略完成，点击了 ${点击次数} 次` };
    } catch (e) {
        console.error("执行点赞出错: " + e.message);
        return { 成功: false, 信息: "执行点赞出错: " + e.message };
    }
}



/**
 * 执行阅读操作（图文内容滑动）
 * 对图文内容进行自然的滑动阅读，模拟真实用户行为
 *
 * @param {Object} 页面信息 - 页面信息对象
 * @returns {Object} - 阅读结果
 */
function 执行阅读操作(页面信息 = null) {
    console.log("📖 开始执行阅读操作");

    try {
        // 检查是否为图文内容
        let 是图文内容 = !页面信息 || !页面信息.是否视频;

        if (!是图文内容) {
            console.log("📺 检测到视频内容，跳过滑动，但仍需阅读延时");

            // 视频内容也需要阅读延时，只是不滑动
            let 配置 = 读取配置文件();
            let 阅读等待时间 = 配置.阅读时间 || 10000; // 使用配置文件中的阅读时间，默认10秒
            console.log(`📺 视频阅读等待 ${阅读等待时间}ms`);

            等待(阅读等待时间);

            console.log("✅ 视频阅读操作完成");
            return { 成功: true, 信息: `视频阅读完成，阅读${阅读等待时间}ms` };
        }

        console.log("📄 检测到图文内容，开始滑动阅读");

        // 使用DeviceOperation中的刷视频滑动功能
        let 滑动结果 = DeviceOperation.刷视频滑动();

        if (滑动结果.成功) {
            // 滑动后等待配置文件中设置的阅读时间
            let 配置 = 读取配置文件();
            let 阅读等待时间 = 配置.阅读时间 || 10000; // 使用配置文件中的阅读时间，默认10秒
            console.log(`📖 滑动完成，开始阅读等待 ${阅读等待时间}ms`);

            等待(阅读等待时间);

            console.log("✅ 阅读操作完成");
            return { 成功: true, 信息: `阅读完成，${滑动结果.信息}，阅读${阅读等待时间}ms` };
        } else {
            console.log("❌ 滑动失败");
            return { 成功: false, 信息: `滑动操作失败: ${滑动结果.信息}` };
        }

    } catch (e) {
        console.error("❌ 执行阅读操作出错:", e.message);
        return { 成功: false, 信息: "执行阅读操作出错: " + e.message };
    }
}

/**
 * 执行收藏操作
 *
 * @returns {Object} - 收藏结果
 */
function 执行收藏(页面信息 = null) {
    console.log("执行收藏操作");

    try {
        // 优先使用精确坐标点击
        if (页面信息 && 页面信息.收藏按钮坐标) {
            console.log("使用精确坐标执行收藏操作");
            let 坐标 = 页面信息.收藏按钮坐标;

            console.log(`收藏按钮坐标: 中心点(${坐标.中心X}, ${坐标.中心Y})`);

            // 在收藏按钮中心点附近随机偏移5像素，模拟真实点击
            let 点击X = 坐标.中心X + (Math.random() * 10 - 5);
            let 点击Y = 坐标.中心Y + (Math.random() * 10 - 5);

            console.log(`实际点击坐标: (${Math.round(点击X)}, ${Math.round(点击Y)})`);

            // 执行精确点击
            DeviceOperation.点击(点击X, 点击Y);

            // 等待操作完成
            随机等待("点击");

            console.log("精确收藏点击完成");
            return { 成功: true, 信息: "使用精确坐标收藏成功" };
        }

        // 如果没有坐标信息，获取当前页面信息
        if (!页面信息) {
            console.log("未提供页面信息，尝试获取当前页面信息");
            页面信息 = 获取页面信息();

            if (页面信息 && 页面信息.收藏按钮坐标) {
                console.log("获取到收藏按钮坐标，使用精确点击");
                let 坐标 = 页面信息.收藏按钮坐标;

                let 点击X = 坐标.中心X + (Math.random() * 10 - 5);
                let 点击Y = 坐标.中心Y + (Math.random() * 10 - 5);

                console.log(`收藏按钮精确点击: (${Math.round(点击X)}, ${Math.round(点击Y)})`);
                DeviceOperation.点击(点击X, 点击Y);
                随机等待("点击");

                return { 成功: true, 信息: "使用获取的精确坐标收藏成功" };
            }
        }

        // 备用方案：使用随机点击策略
        console.log("⚠️ 未找到收藏按钮坐标，使用备用随机点击策略");

        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;

        // 屏幕中间位置，稍微偏右下一点（收藏按钮通常在点赞按钮右侧）
        let 中心X = (屏幕宽度 / 2) + 150;  // 更往右偏移，确保点到收藏按钮
        let 中心Y = (屏幕高度 / 2);

        // 生成随机点击次数（3-5次）
        let 点击次数 = Math.floor(Math.random() * 3) + 3;
        console.log(`将在屏幕中间偏右区域随机点击 ${点击次数} 次`);

        // 第一次点击位置 - 在中心点周围随机100像素范围内生成坐标
        let 当前X = 中心X + (Math.random() * 200 - 100);
        let 当前Y = 中心Y + (Math.random() * 200 - 100);

        // 确保坐标在屏幕内
        当前X = Math.max(10, Math.min(屏幕宽度 - 10, 当前X));
        当前Y = Math.max(10, Math.min(屏幕高度 - 10, 当前Y));

        // 执行随机点击
        for (let i = 0; i < 点击次数; i++) {
            // 后续点击在前一次位置基础上偏移10像素内
            if (i > 0) {
                当前X += (Math.random() * 20 - 10); // ±10像素偏移
                当前Y += (Math.random() * 20 - 10); // ±10像素偏移

                // 确保坐标在屏幕内
                当前X = Math.max(10, Math.min(屏幕宽度 - 10, 当前X));
                当前Y = Math.max(10, Math.min(屏幕高度 - 10, 当前Y));
            }

            console.log(`随机点击 #${i + 1}: (${Math.floor(当前X)}, ${Math.floor(当前Y)})`);

            // 直接使用DeviceOperation.点击，不需要再判断模式
            DeviceOperation.点击(当前X, 当前Y);

            // 随机等待20-50毫秒
            let 随机等待时间 = Math.floor(Math.random() * 31) + 20;
            sleep(随机等待时间);
        }

        // 最后等待500毫秒
        等待(500);

        return { 成功: true, 信息: `随机点击策略完成，点击了 ${点击次数} 次` };
    } catch (e) {
        console.error("执行收藏出错: " + e.message);
        return { 成功: false, 信息: "执行收藏出错: " + e.message };
    }
}

/**
 * 开始小红书本地操作
 * 主要流程：读取本地URL -> 打开小红书 -> 执行点赞和收藏操作 -> 记录日志
 * 支持断点续传，自动从上次停止位置继续
 *
 * @returns {Object} - 操作结果
 */
function 开始小红书本地操作() {
    console.log("开始本地操作模式 - 新账号，重置操作计数");

    // 重置本地操作计数（新账号从零开始）
    重置本地操作计数();
    console.log("已重置操作计数，新账号从零开始");

    // 获取当前操作行号
    let 当前行号 = 获取配置项("当前操作行号", 1);

    // 显示当前进度
    显示当前进度();

    // 显示当前位置信息
    if (当前行号 === 1) {
        console.log("📍 从第1行开始读取URL");
        toast("从第1行开始");
    } else {
        console.log(`📍 断点续传，从第${当前行号}行继续读取URL`);
        toast(`断点续传，从第${当前行号}行继续`);
    }
    // 打开小红书应用
    if (!打开小红书()) {
        toast("打开小红书应用失败");
        return { success: false, message: "打开小红书应用失败" };
    }

    // 检查是否出现账号异常提示
    if (检查账号异常提示()) {
        console.log("检测到账号异常提示，返回给外层处理换号逻辑");
        toast("检测到账号异常");
        return {
            success: false,
            message: "账号异常，需要换号",
            needChangeAccount: true
        };
    }
    返回主界面()
    // 初始化计数器
    let 已完成操作次数 = 0;
    let 总操作次数 = 本地操作计数.当前点赞数 + 本地操作计数.当前收藏数 + 本地操作计数.当前阅读数;

    console.log(`开始本地操作循环，当前计数: 点赞${本地操作计数.当前点赞数}, 收藏${本地操作计数.当前收藏数}, 阅读${本地操作计数.当前阅读数}`);
    toast(`开始本地操作，当前总操作数: ${总操作次数}`);

    // 主循环
    while (true) {

        // 获取下一个URL
        console.log(`[操作${已完成操作次数 + 1}] 获取下一个URL...`);
        let 获取URL结果 = 获取下一个URL();
        // 检查获取结果
        if (!获取URL结果.success) {
            if (获取URL结果.status === "limit_reached") {
                console.log("部分操作已达到目标数量，当前账号操作完成");
                toast("当前账号操作完成");
                return { success: true, message: "部分操作已达到目标数量，当前账号操作完成" };
            } else if (获取URL结果.status === "insufficient_links") {
                console.log("链接数量不足以完成所有目标操作，当前账号操作完成");
                toast("链接不足，操作完成");
                return { success: true, message: "链接数量不足以完成所有目标操作，当前账号操作完成" };
            } else if (获取URL结果.status === "no_more_operations") {
                console.log("所有链接都已处理且无可操作内容，当前账号操作完成");
                toast("所有链接已处理完成");
                return { success: true, message: "所有链接都已处理且无可操作内容，当前账号操作完成" };
            } else if (获取URL结果.status === "no_links") {
                console.log("没有可用的URL");
                toast("没有可用的URL");
                return { success: false, message: "没有可用的URL" };
            } else {
                console.error("获取URL失败: " + 获取URL结果.message);
                toast("获取URL失败");
                return { success: false, message: 获取URL结果.message };
            }
        }
        // 获取URL信息
        let 链接 = 获取URL结果.data.url;
        let 当前行号 = 获取URL结果.data.current_line;
        let 总行数 = 获取URL结果.data.total_lines;

        // 详细的进度提示
        let 进度信息 = `📍 处理第${当前行号}/${总行数}个链接`;
        let 计数信息 = `📊 当前进度: 点赞${本地操作计数.当前点赞数}/${获取配置项("每个账号点赞数量", 0)} | 收藏${本地操作计数.当前收藏数}/${获取配置项("每个账号收藏数量", 0)} | 阅读${本地操作计数.当前阅读数}/${获取配置项("每个账号阅读数量", 0)}`;

        console.log(进度信息);
        console.log(计数信息);

        // 🔧 添加链接重复检查
        if (typeof global.上次处理的链接 !== 'undefined' && global.上次处理的链接 === 链接) {
            console.log("⚠️ 警告：检测到重复处理同一个链接！");
            console.log(`⚠️ 上次链接: ${global.上次处理的链接}`);
            console.log(`⚠️ 当前链接: ${链接}`);
            console.log(`⚠️ 当前行号: ${当前行号}`);
            toast("⚠️ 检测到重复链接，可能存在行号递增问题", 3000);
        }
        global.上次处理的链接 = 链接;

        // 弹出详细提示
        toast(`${进度信息}\n${计数信息}`, 3000);

        // ===== 新的4步重试逻辑开始 =====
        // 🎯 第1步尝试: 浏览器打开链接 → 获取文章信息A → 返回首页不刷新 → 点击第一篇比较
        console.log("🌐 第1步尝试: 浏览器打开链接，获取目标文章信息");
        if (!打开链接(链接)) {
            console.error("❌ 打开链接失败");
            toast("❌ 打开链接失败");
            处理链接操作完成(链接, "error", false, "打开链接失败");
            continue;
        }
        // 等待页面加载
        console.log("⏳ 等待页面加载...");
        //随机等待("跳转链接延时");

        // 获取目标文章信息（页面信息A）- 只在第1步获取，后续复用
        console.log("📄 获取目标文章信息...");
        let 目标文章信息 = 获取页面信息();
        if (!目标文章信息) {
            console.log("❌ 未获取到目标文章信息，跳过此链接");
            toast("❌ 获取目标文章信息失败");
            处理链接操作完成(链接, "error", false, "未获取到目标文章信息");
            continue;
        }
        console.log(`✅ 目标文章信息: ${目标文章信息.标题 || '无标题'}`);

        // 🎯 分析是否需要执行操作（基于目标文章的状态）
        console.log("🎯 分析目标文章状态，判断是否需要操作");

        // 获取配置信息
        let 启用点赞 = 获取配置项("启用点赞", 0) == 1;
        let 启用收藏 = 获取配置项("启用收藏", 0) == 1;
        let 启用阅读 = 获取配置项("启用阅读", 0) == 1;

        let 每个账号点赞数量 = 获取配置项("每个账号点赞数量", 0);
        let 每个账号收藏数量 = 获取配置项("每个账号收藏数量", 0);
        let 每个账号阅读数量 = 获取配置项("每个账号阅读数量", 0);

        // 获取目标文章的当前状态
        let 目标已点赞 = 目标文章信息.已点赞 || false;
        let 目标已收藏 = 目标文章信息.已收藏 || false;

        console.log(`📊 目标文章状态:`);
        console.log(`  - 已点赞: ${目标已点赞 ? "✅ 是" : "❌ 否"}`);
        console.log(`  - 已收藏: ${目标已收藏 ? "✅ 是" : "❌ 否"}`);

        // 判断是否需要执行各种操作
        let 需要点赞 = 启用点赞 && 本地操作计数.当前点赞数 < 每个账号点赞数量 && !目标已点赞;
        let 需要收藏 = 启用收藏 && 本地操作计数.当前收藏数 < 每个账号收藏数量 && !目标已收藏;
        let 需要阅读 = 启用阅读 && 本地操作计数.当前阅读数 < 每个账号阅读数量;

        console.log(`🎯 操作需求分析:`);
        console.log(`  - 需要点赞: ${需要点赞 ? "✅ 是" : "❌ 否"} ${!需要点赞 && 启用点赞 && 目标已点赞 ? "(已点过赞)" : ""} ${!需要点赞 && 启用点赞 && 本地操作计数.当前点赞数 >= 每个账号点赞数量 ? "(已达上限)" : ""}`);
        console.log(`  - 需要收藏: ${需要收藏 ? "✅ 是" : "❌ 否"} ${!需要收藏 && 启用收藏 && 目标已收藏 ? "(已收藏过)" : ""} ${!需要收藏 && 启用收藏 && 本地操作计数.当前收藏数 >= 每个账号收藏数量 ? "(已达上限)" : ""}`);
        console.log(`  - 需要阅读: ${需要阅读 ? "✅ 是" : "❌ 否"} ${!需要阅读 && 启用阅读 && 本地操作计数.当前阅读数 >= 每个账号阅读数量 ? "(已达上限)" : ""}`);

        // 如果不需要任何操作，直接跳过，无需返回首页
        if (!需要点赞 && !需要收藏 && !需要阅读) {
            let 跳过原因 = [];
            if (启用点赞 && 目标已点赞) 跳过原因.push("已点过赞");
            if (启用收藏 && 目标已收藏) 跳过原因.push("已收藏过");
            if (启用点赞 && 本地操作计数.当前点赞数 >= 每个账号点赞数量) 跳过原因.push("点赞已达上限");
            if (启用收藏 && 本地操作计数.当前收藏数 >= 每个账号收藏数量) 跳过原因.push("收藏已达上限");
            if (启用阅读 && 本地操作计数.当前阅读数 >= 每个账号阅读数量) 跳过原因.push("阅读已达上限");

            let 跳过信息 = `⏭️ 无需操作，直接跳过: ${跳过原因.join(", ")}`;
            console.log(跳过信息);
            toast(跳过信息, 2000);

            // 跳过的链接也需要递增行号
            处理链接操作完成(链接, "skip", true, 跳过原因.join(", "));
            console.log("🚀 优化：无需操作直接跳过，节省了返回首页的时间");
            continue;
        }

        // 如果需要操作，显示状态提示
        let 状态提示 = "";
        if (目标已点赞) 状态提示 += "⚠️ 已点过赞 ";
        if (目标已收藏) 状态提示 += "⚠️ 已收藏过 ";
        if (状态提示) {
            toast(状态提示.trim(), 2000);
            console.log(`🔔 状态提示: ${状态提示.trim()}`);
        }

        console.log("🎯 需要执行操作，开始4步重试逻辑...");

        // ===== 4步重试循环开始 =====
        let 最终操作页面信息 = null;
        let 匹配成功 = false;

        for (let 尝试次数 = 5; 尝试次数 <= 4; 尝试次数++) { //不执行里面的操作了
            console.log(`\n🔄 开始第${尝试次数}次尝试...`);
            if (尝试次数 === 1) {
                // 🎯 第1次尝试: 返回首页不刷新，点击第一篇比较
                console.log("🏠 第1次尝试: 返回首页(不刷新) → 点击第一篇文章比较");
                返回主界面();

            } else if (尝试次数 === 2) {
                // 🎯 第2次尝试: 先返回首页 → 浏览器打开链接 → 返回首页下拉刷新 → 点击第一篇比较
                console.log("🏠 第2次尝试: 先返回首页(清理第1次的状态)");
                返回主界面();

                console.log("🌐 第2次尝试: 浏览器再次打开链接");
                if (!打开链接(链接)) {
                    console.error("❌ 第2次打开链接失败");
                    continue;
                }
                console.log("🏠 返回首页并下拉刷新");
                返回主界面();
                console.log("📱 执行下拉刷新操作");
                执行首页下拉刷新();

            } else if (尝试次数 === 3) {
                // 🎯 第3次尝试: 关闭小红书 → 浏览器打开链接 → 返回首页 → 点击第一篇比较
                console.log("❌ 第3次尝试: 关闭小红书APP");
                console.log("📱 强制关闭小红书APP");
                强制关闭小红书APP();

                console.log("🌐 重新通过浏览器打开链接");
                if (!打开链接(链接)) {
                    console.error("❌ 第3次打开链接失败");
                    continue;
                }
                console.log("🏠 返回首页");
                返回主界面();

            } else if (尝试次数 === 4) {
                // 🎯 第4次尝试: 直接通过浏览器打开链接并操作，不再比较
                console.log("🌐 第4次尝试: 直接通过浏览器打开链接操作(不再比较)");
                if (!打开链接(链接)) {
                    console.error("❌ 第4次打开链接失败");
                    break;
                }
                console.log("⏳ 等待页面加载...");
                随机等待("跳转链接延时");

                // 直接获取页面信息并操作，不再比较
                let 直接操作页面信息 = 获取页面信息();
                if (直接操作页面信息) {
                    console.log("✅ 第4次尝试: 直接在浏览器页面操作，跳过比较");
                    最终操作页面信息 = 直接操作页面信息;
                    匹配成功 = true;
                    break;
                } else {
                    console.log("❌ 第4次尝试: 获取页面信息失败");
                    break;
                }
            }

            // 前3次尝试都需要点击首页第一篇文章并比较
            if (尝试次数 <= 3) {
                console.log(`👆 第${尝试次数}次尝试: 点击首页第一篇文章`);
                if (!点击首篇文章()) {
                    console.log(`❌ 第${尝试次数}次尝试: 点击首页第一篇文章失败`);
                    continue;
                }

                // 获取首页文章信息（页面信息B）
                console.log(`📄 第${尝试次数}次尝试: 获取首页文章信息...`);
                let 首页文章信息 = 获取页面信息();
                if (!首页文章信息) {
                    console.log(`❌ 第${尝试次数}次尝试: 未获取到首页文章信息`);
                    continue;
                }
                console.log(`✅ 第${尝试次数}次尝试: 首页文章信息: ${首页文章信息.标题 || '无标题'}`);

                // 对比文章信息
                console.log(`� 第${尝试次数}次尝试: 对比文章信息`);
                let 是同一篇文章 = 比较页面内容(目标文章信息, 首页文章信息);

                if (是同一篇文章) {
                    console.log(`✅ 第${尝试次数}次尝试: 确认是同一篇文章，匹配成功！`);
                    toast(`✅ 第${尝试次数}次尝试匹配成功`, 2000);
                    最终操作页面信息 = 首页文章信息;
                    匹配成功 = true;
                    break;
                } else {
                    console.log(`❌ 第${尝试次数}次尝试: 不是同一篇文章，继续下一次尝试`);
                    toast(`❌ 第${尝试次数}次尝试不匹配`, 1500);
                }
            }
        }
// 上面循环不执行操作了.这里也不执行判断
/*         // 检查4次尝试的最终结果
        if (!匹配成功 || !最终操作页面信息) {
            console.log("❌ 4次尝试都失败，跳过此链接");
            toast("❌ 4次尝试都失败，跳过此链接", 3000);
            处理链接操作完成(链接, "error", false, "4次尝试都失败");
            continue;
        }

        console.log("✅ 匹配成功，准备执行操作");
        // ===== 4步重试循环结束 =====
 */
        // 步骤5: 开始执行操作（按顺序：点赞 → 收藏 → 阅读）
        最终操作页面信息=目标文章信息
        console.log("🎯 步骤5: 开始执行操作");
        console.log(`📊 当前页面状态: 已点赞=${最终操作页面信息.已点赞 ? '是' : '否'}, 已收藏=${最终操作页面信息.已收藏 ? '是' : '否'}`);

        // 注意：操作需求分析已在步骤2完成，这里直接使用之前的结果
        let 实际操作类型 = [];
        let 操作成功 = true;
        let 错误信息 = null;

        // 步骤5.1: 执行点赞操作
        if (需要点赞) {
            console.log("👍 步骤5.1: 开始执行点赞操作...");
            toast(`👍 正在点赞 (${本地操作计数.当前点赞数 + 1}/${每个账号点赞数量})`, 1500);

            let 点赞结果 = 执行点赞(最终操作页面信息);
            if (点赞结果.成功) {
                更新本地操作计数("like", true);
                实际操作类型.push("like");
                let 点赞成功信息 = `✅ 点赞成功! 当前: ${本地操作计数.当前点赞数}/${每个账号点赞数量}`;
                console.log(点赞成功信息);
                toast(点赞成功信息, 2000);
            } else {
                操作成功 = false;
                错误信息 = 错误信息 ? 错误信息 + ", 点赞失败" : "点赞失败";
                let 点赞失败信息 = `❌ 点赞失败: ${点赞结果.信息}`;
                console.log(点赞失败信息);
                toast(点赞失败信息, 2000);
            }
        } else if (启用点赞 && 目标已点赞) {
            let 已点赞信息 = "⚠️ 文章已被点赞，跳过点赞操作";
            console.log(已点赞信息);
            toast(已点赞信息, 1500);
        } else if (启用点赞 && 本地操作计数.当前点赞数 >= 每个账号点赞数量) {
            let 达上限信息 = `⚠️ 点赞数已达上限 (${本地操作计数.当前点赞数}/${每个账号点赞数量})`;
            console.log(达上限信息);
            toast(达上限信息, 1500);
        }

        // 操作间隔等待
        if (需要点赞 && 需要收藏) {
            console.log("⏳ 点赞完成，等待后执行收藏...");
            随机等待("操作间隔");
        }

        // 步骤5.2: 执行收藏操作
        if (需要收藏) {
            console.log("⭐ 步骤5.2: 开始执行收藏操作...");
            toast(`⭐ 正在收藏 (${本地操作计数.当前收藏数 + 1}/${每个账号收藏数量})`, 1500);

            let 收藏结果 = 执行收藏(最终操作页面信息);
            if (收藏结果.成功) {
                更新本地操作计数("collect", true);
                实际操作类型.push("collect");
                let 收藏成功信息 = `✅ 收藏成功! 当前: ${本地操作计数.当前收藏数}/${每个账号收藏数量}`;
                console.log(收藏成功信息);
                toast(收藏成功信息, 2000);
            } else {
                操作成功 = false;
                错误信息 = 错误信息 ? 错误信息 + ", 收藏失败" : "收藏失败";
                let 收藏失败信息 = `❌ 收藏失败: ${收藏结果.信息}`;
                console.log(收藏失败信息);
                toast(收藏失败信息, 2000);
            }
        } else if (启用收藏 && 目标已收藏) {
            let 已收藏信息 = "⚠️ 文章已被收藏，跳过收藏操作";
            console.log(已收藏信息);
            toast(已收藏信息, 1500);
        } else if (启用收藏 && 本地操作计数.当前收藏数 >= 每个账号收藏数量) {
            let 收藏达上限信息 = `⚠️ 收藏数已达上限 (${本地操作计数.当前收藏数}/${每个账号收藏数量})`;
            console.log(收藏达上限信息);
            toast(收藏达上限信息, 1500);
        }

        // 操作间隔等待
        if ((需要点赞 || 需要收藏) && 需要阅读) {
            console.log("⏳ 点赞/收藏完成，等待后执行阅读...");
            随机等待("操作间隔");
        }

        // 步骤5.3: 执行阅读操作（在点赞和收藏之后）
        if (需要阅读) {
            console.log("📖 步骤5.3: 开始执行阅读操作...");
            toast(`📖 正在阅读 (${本地操作计数.当前阅读数 + 1}/${每个账号阅读数量})`);

            let 阅读结果 = 执行阅读操作(最终操作页面信息);
            if (阅读结果.成功) {
                更新本地操作计数("read", true);
                实际操作类型.push("read");
                let 阅读成功信息 = `✅ 阅读完成! 当前: ${本地操作计数.当前阅读数}/${每个账号阅读数量}`;
                console.log(阅读成功信息);
                toast(阅读成功信息, 2000);
            } else {
                // 阅读失败不影响整体操作成功状态，只记录日志
                let 阅读失败信息 = `⚠️ 阅读操作失败: ${阅读结果.信息}`;
                console.log(阅读失败信息);
                toast(阅读失败信息, 1500);
            }
        } else if (启用阅读 && 本地操作计数.当前阅读数 >= 每个账号阅读数量) {
            let 阅读达上限信息 = `⚠️ 阅读数已达上限 (${本地操作计数.当前阅读数}/${每个账号阅读数量})`;
            console.log(阅读达上限信息);
            toast(阅读达上限信息, 1500);
        }

        // 记录操作日志并递增行号
        let 操作类型描述 = 实际操作类型.length > 0 ? 实际操作类型.join("+") : "read";
        处理链接操作完成(链接, 操作类型描述, 操作成功, 错误信息);

        // 详细的操作完成信息
        let 操作结果图标 = 操作成功 ? "✅" : "❌";
        let 操作完成信息 = `${操作结果图标} 链接操作完成`;
        console.log(`${操作完成信息} - 类型: ${操作类型描述}, 结果: ${操作成功 ? "成功" : "失败"}`);

        // 更新计数
        已完成操作次数++;

        // 显示详细的进度总结
        let 总进度信息 = `📊 操作总结 (第${当前行号}/${总行数}个链接):\n` +
            `👍 点赞: ${本地操作计数.当前点赞数}/${每个账号点赞数量}\n` +
            `⭐ 收藏: ${本地操作计数.当前收藏数}/${每个账号收藏数量}\n` +
            `📖 阅读: ${本地操作计数.当前阅读数}/${每个账号阅读数量}\n` +
            `🔗 已处理: ${已完成操作次数} 个链接`;

        console.log(总进度信息);
        toast(总进度信息);

        // 步骤6: 返回主界面，准备下一次循环
        console.log("步骤6: 返回主界面...");
        返回主界面();
        //随机等待("跳转链接延时");
    }

    // 注意：正常情况下不会执行到这里，因为循环会通过return语句退出
}

/**
 * 更新本地操作信息
 * 
 * @param {Object} 更新结果 - 更新操作状态API的返回结果
 */
function 更新操作信息(更新结果) {
    // 获取当前操作信息
    操作信息 = 获取操作信息();

    // 使用服务端返回的剩余操作次数更新
    if (更新结果 && 更新结果.success && 更新结果.data && 更新结果.data.remaining_operations !== undefined) {
        操作信息.剩余操作次数 = 更新结果.data.remaining_operations;
        console.log(`从服务端获取剩余操作次数: ${操作信息.剩余操作次数}`);
    }

    console.log(`当前操作信息 - 剩余: ${操作信息.剩余操作次数}`);

    // 检查是否已达到上限
    if (操作信息.剩余操作次数 <= 0) {
        console.log("已达到每日操作上限");
        toast("今日操作次数已达上限");
    }
}

/**
 * 等待指定时间
 * 
 * @param {number} 毫秒 - 等待的毫秒数
 */
function 等待(毫秒) {
    sleep(毫秒);
}

/**
 * 随机等待一段时间（数字参数版本）
 *
 * @param {number} 最小毫秒 - 最小等待时间
 * @param {number} 最大毫秒 - 最大等待时间
 */
function 随机等待数字(最小毫秒, 最大毫秒) {
    let 等待时间 = 最小毫秒 + Math.floor(Math.random() * (最大毫秒 - 最小毫秒));
    console.log("随机等待 " + 等待时间 + " 毫秒");
    等待(等待时间);
}

// 在等待函数后添加以下函数

/**
 * 初始化本地操作
 * 在脚本启动时调用，加载配置和计数
 *
 * @returns {boolean} - 是否需要从头开始
 */
function 初始化本地操作() {
    console.log("初始化本地操作...");

    // 重新加载配置
    本地配置 = 读取配置文件();
    console.log("配置加载完成");

    // 加载操作计数
    加载本地操作计数();
    console.log("操作计数加载完成");

    // 检查是否需要从头开始
    let 从头开始 = 获取配置项("从头开始读取", false);
    if (从头开始) {
        console.log("⚠️  配置文件设置为从头开始读取");
        // 重置配置文件中的设置，避免下次自动从头开始
        本地配置["从头开始读取"] = false;
        // 这里可以选择是否保存回配置文件，暂时不保存，只在内存中修改
    }

    // 显示当前状态
    let 点赞上限 = 获取配置项("每个账号点赞数量", 50);
    let 收藏上限 = 获取配置项("每个账号收藏数量", 30);
    let 阅读上限 = 获取配置项("每个账号阅读数量", 100);

    console.log(`当前操作状态:`);
    console.log(`  点赞: ${本地操作计数.当前点赞数}/${点赞上限}`);
    console.log(`  收藏: ${本地操作计数.当前收藏数}/${收藏上限}`);
    console.log(`  阅读: ${本地操作计数.当前阅读数}/${阅读上限}`);

    return 从头开始;
}

/**
 * 打开小红书应用
 */
function 打开小红书() {
    console.log("打开小红书应用");
    let 应用包名 = "com.xingin.xhs";
    try {
        // 优先尝试ROOT模式下am命令启动
        if (DeviceOperation.获取交互操作模式() === 3) { // ROOT模式
            console.log("当前为ROOT模式，尝试am命令启动应用");
            let result = shell("am start -n com.xingin.xhs/.SplashActivity", true);
            等待(1200);
            if (typeof currentPackage === "function" && currentPackage() === 应用包名) {
                console.log("am命令后检测到小红书已在前台，视为成功");
                return true;
            } else {
                console.warn("am命令启动后未检测到小红书，尝试monkey命令");
                let monkeyResult = shell('monkey -p com.xingin.xhs -c android.intent.category.LAUNCHER 1', true);
                等待(1500);
                if (typeof currentPackage === "function" && currentPackage() === 应用包名) {
                    console.log("monkey命令后检测到小红书已在前台，视为成功");
                    return true;
                } else {
                    console.error("monkey命令后小红书未在前台: " + monkeyResult.error);
                    return false;
                }
            }
        }
        // 非ROOT模式也用monkey命令
        console.log("非ROOT模式，直接用monkey命令启动");
        let monkeyResult = shell('monkey -p com.xingin.xhs -c android.intent.category.LAUNCHER 1', true);
        等待(1500);
        if (typeof currentPackage === "function" && currentPackage() === 应用包名) {
            return true;
        } else {
            console.error("monkey命令启动失败: " + monkeyResult.error);
            return false;
        }
    } catch (e) {
        console.error("打开小红书失败: " + e.message);
        return false;
    }
}

/**
 * 返回主界面
 * 多次按返回键，直到回到主界面
 * 优化版：减少查询次数和等待时间，兼容ROOT模式
 * 
 * @returns {boolean} - 是否成功返回主界面
 */
function 返回主界面() {
    console.log("返回主界面");

    // 首页可能出现的关键词
    const 首页关键词 = ["首页", "发现", "关注", "直播", "短剧", "附近", "热门", "消息", "我", "推荐", "穿搭", "情感"];

    // 最多按5次返回键
    for (let i = 0; i < 5; i++) {
        // 检查是否已在主界面
        let 找到的关键词 = 0;

        try {
            // 根据操作模式选择不同的检查方法
            if (DeviceOperation.获取交互操作模式() === 3) {  // ROOT模式
                // 使用DeviceOperation的方法获取XML并解析
                let pageXml = DeviceOperation.获取窗口XML();
                if (pageXml) {
                    // 检查有多少关键词存在于XML中
                    for (let 关键词 of 首页关键词) {
                        if (pageXml.includes(`text="${关键词}"`) ||
                            pageXml.includes(`text="${关键词} "`) ||
                            pageXml.includes(`"${关键词}"`) ||
                            pageXml.includes(`content-desc="${关键词}"`)) {
                            找到的关键词++;

                            // 如果找到3个或以上关键词，认为已在首页
                            if (找到的关键词 >= 3) {
                                console.log(`ROOT模式下已找到 ${找到的关键词} 个首页关键词，确认在首页`);
                                return true;
                            }
                        }
                    }
                }
            } else {  // 无障碍模式已被禁用
                console.error("❌ 无障碍模式已被禁用，无法检查首页状态");
                return false;
            }
        } catch (e) {
            console.error("检查首页元素出错: " + e.message);
        }

        // 按返回键
        console.log("按返回键");

        // 检查当前交互模式
        if (DeviceOperation.获取交互操作模式() === 3) {
            // ROOT模式：使用设备操作模块的返回方法
            console.log("使用ROOT模式执行返回操作");
            DeviceOperation.返回();
        } else {
            console.error("脚本已配置为仅支持ROOT模式");
        }

        等待(1000);
    }

    // 最后检查一次
    let 找到的关键词 = 0;

    if (DeviceOperation.获取交互操作模式() === 3) {  // ROOT模式
        let pageXml = DeviceOperation.获取窗口XML();
        if (pageXml) {
            for (let 关键词 of 首页关键词) {
                if (pageXml.includes(`text="${关键词}"`) ||
                    pageXml.includes(`text="${关键词} "`) ||
                    pageXml.includes(`"${关键词}"`) ||
                    pageXml.includes(`content-desc="${关键词}"`)) {
                    找到的关键词++;
                }
            }
        }
    } else {  // 无障碍模式已被禁用
        console.error("❌ 无障碍模式已被禁用");
        return false;
    }

    if (找到的关键词 >= 3) {
        console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
        return true;
    }

    console.log("未能确认返回首页");
    return false;
}

/**
 * 执行首页下拉刷新
 * 在小红书首页执行下拉刷新操作
 *
 * @returns {boolean} - 是否成功执行刷新
 */
function 执行首页下拉刷新() {
    console.log("📱 开始执行首页下拉刷新");

    try {
        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;

        // 计算下拉刷新的起始和结束坐标
        let 起始X = 屏幕宽度 / 2;
        let 起始Y = 屏幕高度 * 0.3;  // 从屏幕30%的位置开始
        let 结束X = 起始X;
        let 结束Y = 屏幕高度 * 0.7;  // 拖拽到屏幕70%的位置

        console.log(`下拉刷新坐标: (${起始X}, ${起始Y}) → (${结束X}, ${结束Y})`);

        // 执行下拉手势
        gesture(800, [起始X, 起始Y], [结束X, 结束Y]);

        // 等待刷新完成
        console.log("⏳ 等待下拉刷新完成...");
        sleep(2000);

        console.log("✅ 首页下拉刷新完成");
        return true;

    } catch (e) {
        console.error("❌ 执行首页下拉刷新失败: " + e.message);
        return false;
    }
}

/**
 * 强制关闭小红书APP
 * 使用多种方式强制关闭小红书应用
 *
 * @returns {boolean} - 是否成功关闭APP
 */
function 强制关闭小红书APP() {
    console.log("📱 开始强制关闭小红书APP");

    try {
        let 应用包名 = "com.xingin.xhs";

        // 方式1: 使用shell命令强制停止应用
        if (DeviceOperation.获取交互操作模式() === 3) { // ROOT模式
            console.log("使用ROOT权限强制停止应用");
            let result = shell(`am force-stop ${应用包名}`, true);
            sleep(1000);

            // 检查是否成功关闭
            if (typeof currentPackage === "function" && currentPackage() !== 应用包名) {
                console.log("✅ ROOT命令成功关闭小红书");
                return true;
            }
        }

        // 方式2: 使用最近任务关闭
        console.log("尝试通过最近任务关闭应用");
        recents(); // 打开最近任务
        sleep(1000);

        // 查找小红书应用并向上滑动关闭
        let 小红书卡片 = text("小红书").findOne(3000);
        if (小红书卡片) {
            let 卡片坐标 = 小红书卡片.bounds();
            let 中心X = 卡片坐标.centerX();
            let 中心Y = 卡片坐标.centerY();

            // 向上滑动关闭
            gesture(500, [中心X, 中心Y], [中心X, 中心Y - 300]);
            sleep(1000);
            console.log("✅ 通过最近任务关闭小红书");
        }

        // 方式3: 按Home键确保回到桌面
        console.log("按Home键回到桌面");
        home();
        sleep(1000);

        console.log("✅ 小红书APP关闭完成");
        return true;

    } catch (e) {
        console.error("❌ 强制关闭小红书APP失败: " + e.message);
        return false;
    }
}

/**
 * 处理权限弹窗（自动分发）
 * 检查并点击各种常见的权限按钮
 *
 * @returns {boolean} - 是否找到并点击了权限按钮
 */
function 处理权限弹窗() {
    console.log("检查权限弹窗...");
    const 权限按钮文本列表 = ["允许", "始终允许", "确定", "继续", "同意", "确认", "好的"];
    if (DeviceOperation.获取交互操作模式() === 3) {
        let pageXml = DeviceOperation.获取窗口XML();
        if (!pageXml) return false;
        for (let 按钮文本 of 权限按钮文本列表) {
            if (pageXml.includes(按钮文本)) {
                // 使用从XML中查找元素方法
                let 元素 = DeviceOperation.从XML中查找元素(pageXml, { text: 按钮文本 }, false);
                if (元素) {
                    let bounds = DeviceOperation.获取元素bounds(元素);
                    if (bounds) {
                        let x = (bounds.left + bounds.right) / 2;
                        let y = (bounds.top + bounds.bottom) / 2;
                        let 点击结果 = DeviceOperation.点击(x, y);
                        if (点击结果) {
                            console.log(`已点击 "${按钮文本}" 按钮`);
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    } else if (DeviceOperation.获取交互操作模式() === 1) {
        for (let i = 0; i < 权限按钮文本列表.length; i++) {
            let 按钮文本 = 权限按钮文本列表[i];
            let 按钮 = text(按钮文本).findOne(1000);
            if (按钮 && 按钮.clickable()) {
                按钮.click();
                sleep(500);
                return true;
            }
        }
        for (let i = 0; i < 权限按钮文本列表.length; i++) {
            let 按钮文本 = 权限按钮文本列表[i];
            let 元素 = textContains(按钮文本).findOne(500);
            if (元素) {
                元素.click();
                sleep(500);
                return true;
            }
        }
        return false;
    } else {
        console.error("未知操作模式，无法处理权限弹窗");
        return false;
    }
}

/**
 * 检查账号异常提示（自动分发）
 * 检测是否出现需要更换账号的提示，如账号下线、登录过期等
 * 优化版本：减少查询次数，提高执行效率
 * 
 * @returns {boolean} - 是否检测到账号异常提示
 */
function 检查账号异常提示() {
    console.log("检查账号异常提示...");
    try {
        let 异常关键词 = ["账号下线", "下线提示", "登录过期", "请重新登录", "其他设备登录", "账号已被冻结", "账号异常", "违反社区规定"];
        if (DeviceOperation.获取交互操作模式() === 3) {
            let pageXml = DeviceOperation.获取窗口XML();
            if (!pageXml) return false;

            // 改进ROOT模式下的文本匹配逻辑
            // 1. 检查text属性
            for (let k of 异常关键词) {
                if (pageXml.includes(`text="${k}"`) ||
                    pageXml.includes(`text="${k} "`) ||
                    pageXml.includes(`text=" ${k}"`) ||
                    pageXml.includes(`text=" ${k} "`)) {
                    console.log(`检测到账号异常提示文本: ${k}`);
                    return true;
                }
            }

            // 2. 检查content-desc属性
            for (let k of 异常关键词) {
                if (pageXml.includes(`content-desc="${k}"`) ||
                    pageXml.includes(`content-desc="${k} "`) ||
                    pageXml.includes(`content-desc=" ${k}"`) ||
                    pageXml.includes(`content-desc=" ${k} "`)) {
                    console.log(`检测到账号异常提示描述: ${k}`);
                    return true;
                }
            }

            // 3. 检查部分匹配
            for (let k of 异常关键词) {
                if (pageXml.includes(k)) {
                    // 再次验证是否在文本内容中
                    let textMatch = pageXml.match(new RegExp(`text="[^"]*${k}[^"]*"`));
                    let descMatch = pageXml.match(new RegExp(`content-desc="[^"]*${k}[^"]*"`));
                    if (textMatch || descMatch) {
                        console.log(`检测到账号异常提示(部分匹配): ${k}`);
                        return true;
                    }
                }
            }

            return false;
        } else if (DeviceOperation.获取交互操作模式() === 1) {
            let 所有文本元素 = textMatches(".*").find();
            for (let i = 0; i < 所有文本元素.length; i++) {
                let 文本内容 = 所有文本元素[i].text();
                if (!文本内容) continue;
                for (let j = 0; j < 异常关键词.length; j++) {
                    if (文本内容.includes(异常关键词[j])) {
                        console.log(`检测到账号异常提示: "${文本内容}" 包含关键词 "${异常关键词[j]}"`);
                        return true;
                    }
                }
            }
            return false;
        } else {
            console.error("未知操作模式，无法检查账号异常");
            return false;
        }
    } catch (e) {
        console.error("检查账号异常提示出错: " + e.message);
        return false;
    }
}





/**
 * 打开小红书链接（支持短链接、长链接和笔记ID）
 * 一个方法完成所有操作，自动识别链接类型
 * 
 * @param {string} url - 小红书链接或笔记ID
 * @returns {boolean} - 是否成功打开
 */
function 直接打开小红书链接(url) {
    try {
        // 确保url是字符串类型
        url = String(url);
        console.log("准备打开小红书链接: " + url);

        // 如果链接为空
        if (!url || url === "null" || url === "undefined") {
            console.error("无效的链接");
            return false;
        }
        
        // 检查小红书是否已安装
        // if (!app.getPackageName("小红书")) {
        //     console.error("未安装小红书应用");
        //     return false;
        // }
        
        // 情况1: 直接是笔记ID
        if (/^[a-zA-Z0-9_]+$/.test(url) && !url.includes(".")) {
            console.log("检测到笔记ID，直接打开");
            return 直接用ID打开(url);
        }
        
        // 情况2: 小红书长链接
        if (url.includes("xiaohongshu.com")) {
            console.log("检测到小红书长链接，提取ID");
            console.log("URL类型: " + typeof url);
            console.log("URL内容: " + url);

            // 提取笔记ID
            let noteId = 提取笔记ID(url);
            if (noteId) {
                console.log("从长链接提取到笔记ID: " + noteId);
                return 直接用ID打开(noteId);
            } else {
                console.log("未能从长链接提取笔记ID，尝试直接用浏览器打开");
                app.openUrl(url);
                return true;
            }
        }
        
        // 情况3: 小红书短链接，需要先解析
        if (url.includes("xhslink.com")) {
            console.log("检测到小红书短链接，开始解析...");

            // 使用多策略解析短链接
            let resolvedUrl = 解析短链接多策略(url);

            if (resolvedUrl && resolvedUrl !== url) {
                console.log("短链接解析成功: " + resolvedUrl);
                // 递归调用自身处理解析后的URL
                return 直接打开小红书链接(resolvedUrl);
            } else {
                console.log("短链接解析失败，尝试直接打开");
                app.openUrl(url);
                return true;
            }
        }
        
        // 情况4: 其他未识别的链接类型
        console.log("未识别的链接类型，尝试直接用浏览器打开");
        app.openUrl(url);
        return true;
        
    } catch (error) {
        console.error("打开小红书链接失败: " + error.message);
        toast("打开小红书链接失败: " + error.message);
        return false;
    }
    
    // 内部函数：直接用ID打开小红书
    function 直接用ID打开(noteId) {
        try {
            console.log("使用ID打开小红书: " + noteId);
            
            // 构建深度链接并打开
            app.startActivity({
                action: "android.intent.action.VIEW",
                data: "xhsdiscover://item/" + noteId
            });
            console.log("已发送打开请求");
            return true;
        } catch (e) {
            console.error("打开小红书失败: " + e.message);
            return false;
        }
    }
    
    // 内部函数：从URL中提取笔记ID
    function 提取笔记ID(url) {
        try {
            // 确保url是字符串类型
            url = String(url);
            console.log("提取笔记ID - 输入URL: " + url);

            // 尝试提取 /explore/[noteId]
            let match = url.match(/\/explore\/([a-zA-Z0-9_]+)/);
            if (match && match[1]) {
                console.log("通过/explore/路径提取到ID: " + match[1]);
                return match[1];
            }

            // 尝试提取 /discovery/item/[noteId]
            match = url.match(/\/discovery\/item\/([a-zA-Z0-9_]+)/);
            if (match && match[1]) {
                // 如果ID包含参数，只取问号前面的部分
                let noteId = match[1].split('?')[0];
                console.log("通过/discovery/item/路径提取到ID: " + noteId);
                return noteId;
            }

            console.log("未能从URL中提取到笔记ID");
            return null;
        } catch (e) {
            console.error("提取笔记ID出错: " + e.message);
            return null;
        }
    }

    // 多策略解析短链接函数
    function 解析短链接多策略(url) {
        console.log("开始多策略解析短链接: " + url);

        // 策略1: 尝试多种User-Agent
        const userAgents = [
            "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.104 Mobile Safari/537.36",
            "Mozilla/5.0 (Linux; U; Android 11; zh-cn; MI 9 Build/RKQ1.200826.002) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.116 Mobile Safari/537.36"
        ];

        for (let i = 0; i < userAgents.length; i++) {
            try {
                console.log(`尝试User-Agent ${i+1}/${userAgents.length}`);

                let response = http.get(url, {
                    followRedirect: false,
                    timeout: 10000,
                    headers: {
                        "User-Agent": userAgents[i],
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8"
                    }
                });

                console.log(`响应状态码: ${response.statusCode}`);

                // 检查重定向
                if (response.statusCode === 301 || response.statusCode === 302) {
                    let location = response.headers['location'] ||
                                  response.headers['Location'] ||
                                  response.headers['LOCATION'];

                    if (location) {
                        console.log(`找到重定向URL: ${location}`);
                        return String(location);
                    }
                }

                // 检查响应体中的JavaScript重定向
                if (response.statusCode === 200) {
                    let body = response.body.string();
                    let jsRedirect = body.match(/window\.location\.href\s*=\s*['"]([^'"]+)['"]/);
                    if (jsRedirect) {
                        console.log(`找到JavaScript重定向: ${jsRedirect[1]}`);
                        return String(jsRedirect[1]);
                    }
                }

            } catch (e) {
                console.error(`User-Agent ${i+1} 请求失败: ${e.message}`);
                continue;
            }
        }

        // 策略2: 跟随重定向
        try {
            console.log("尝试跟随重定向...");
            let response = http.get(url, {
                followRedirect: true,
                timeout: 15000,
                headers: {
                    "User-Agent": userAgents[0]
                }
            });

            if (response.url && response.url !== url) {
                console.log(`跟随重定向后的URL: ${response.url}`);
                return String(response.url);
            }
        } catch (e) {
            console.error("跟随重定向失败: " + e.message);
        }

        return null;
    }
}







/**
 * 打开链接
 * 
 * @param {string} 链接 - 要打开的链接
 * @returns {boolean} - 是否成功打开
 */
function 打开链接(链接) {
    // 移除打开链接的详细日志
    // 使用通用方式打开链接
    try {
        //app.openUrl(链接);
        //toast("已用浏览器打开链接");
        直接打开小红书链接(链接)
    } catch (e) {
        return false;
    }
    const 最大尝试次数 = 20;
    const 每次尝试间隔 = 1000; // 2秒
    const 总超时时间 = 50000; // 50秒
    let 开始时间 = new Date().getTime();

    for (let i = 0; i < 最大尝试次数; i++) {
        if (new Date().getTime() - 开始时间 > 总超时时间) {
            console.warn("OCR识别超时，退出检测");
            break;
        }
        // 首先检查是否已进入小红书
        // if (DeviceOperation.检查是否进入小红书()) {
        //     console.log("已成功跳转到小红书App");
        //     return true;
        // }
        console.log(`第${i + 1}次OCR识别页面...`);
        let 识别内容 = "*App内打开*|*Chrome*|*允许*|同意";
        // OCR识别全屏文字
        let 结果 = null;
        try {
            // 增加错误处理和重试
            let 重试次数 = 0;
            const 最大OCR重试 = 3;

            while (重试次数 < 最大OCR重试) {
                if (DeviceOperation.检查是否进入小红书()) {
                    console.log("2已成功跳转到小红书App");
                    return true;
                }
                try {
                    结果 = DeviceOperation.获取XML文字信息("", 识别内容, 1, "", true);
                    if (结果 && 结果.length > 0) {
                        DeviceOperation.点击XML关键词(结果, 识别内容);
                        // 点击后等待1秒再检查是否进入小红书
                        sleep(1000);
                        break; // 成功则跳出重试循环
                    }
                } catch (ocrError) {
                    重试次数++;
                    console.error(`OCR识别失败 (${重试次数}/${最大OCR重试}): ${ocrError.message}`);
                    if (重试次数 < 最大OCR重试) {
                        console.log("等待1秒后重试OCR识别...");
                        sleep(1000);
                    } else {
                        console.error("OCR识别重试次数已达上限，跳过本次识别");
                        结果 = null;
                    }
                }
                
            }
        } catch (e) {
            console.error("OCR识别出错: " + e.message);
            // 继续下一次循环，不中断整个流程
        }

        sleep(每次尝试间隔);
    }

    console.warn("未识别到'App内打开'按钮或点击失败");
    return false;
}





// 这个函数已经在文件开头定义了，删除重复定义

/**
 * 点击首篇文章
 * 智能查找并点击首页第一篇文章
 *
 * @returns {boolean} - 是否成功点击
 */
function 点击首篇文章() {
    console.log("点击首页文章");

    try {
        // 检查当前交互模式
        // if (DeviceOperation.获取交互操作模式() !== 3) {
        //     console.error("脚本已配置为仅支持ROOT模式");
        //     return false;
        // }

        // // 获取当前页面XML，尝试找到第一篇文章的位置
        // let pageXml = DeviceOperation.获取窗口XML();
        // if (!pageXml) {
        //     console.log("无法获取页面XML，使用默认坐标点击");
        //     return 使用默认坐标点击();
        // }

        // // 尝试从XML中找到第一篇文章的坐标
        // let 文章坐标 = 从XML中查找首篇文章坐标(pageXml);
        // if (文章坐标) {
        //     console.log(`找到首篇文章坐标: (${文章坐标.x}, ${文章坐标.y})`);
        //     let result = DeviceOperation.点击(文章坐标.x, 文章坐标.y);
        //     if (result && result.成功) {
        //         console.log("✅ 成功点击首篇文章");
        //         return true;
        //     } else {
        //         console.log("点击首篇文章失败，尝试默认坐标");
        //     }
        // }

        // // 如果无法找到具体坐标，使用默认坐标
        // console.log("未找到具体文章坐标，使用默认坐标点击");
        return 使用默认坐标点击();

    } catch (e) {
        console.error("点击首篇文章出错: " + e.message);
        return false;
    }
}

/**
 * 使用默认坐标点击首页文章
 * @returns {boolean} 是否成功
 */
function 使用默认坐标点击() {
    try {
        // 随机化点击坐标 - 首页文章通常在这个区域
        let x = 250 + Math.floor(Math.random() * 40) - 20;  // 250 ± 20
        let y = 500 + Math.floor(Math.random() * 40) - 20;  // 500 ± 20

        console.log(`使用默认坐标点击: (${x}, ${y})`);

        let result = DeviceOperation.点击(x, y);
        if (result && result.成功) {
            console.log("✅ 默认坐标点击成功");
            return true;
        } else {
            console.error("默认坐标点击失败: " + (result ? result.信息 : "未知错误"));
            return false;
        }
    } catch (e) {
        console.error("默认坐标点击出错: " + e.message);
        return false;
    }
}

/**
 * 从XML中查找首篇文章的坐标
 * @param {string} pageXml - 页面XML
 * @returns {Object|null} - 坐标对象 {x, y} 或 null
 */
function 从XML中查找首篇文章坐标(pageXml) {
    try {
        // 查找包含文章信息的元素
        // 小红书首页文章通常包含这些特征：content-desc包含"笔记"或"视频"，以及作者信息
        let 文章模式列表 = [
            /content-desc="(笔记|视频)\s+([^"]+)\s+来自([^"]+)\s+(\d+)赞"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/g,
            /content-desc="(笔记|视频)[^"]*"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/g
        ];

        for (let 模式 of 文章模式列表) {
            let 匹配结果 = pageXml.match(模式);
            if (匹配结果 && 匹配结果.length > 0) {
                // 取第一个匹配的文章
                let 第一个匹配 = 匹配结果[0];
                let 坐标匹配 = 第一个匹配.match(/bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);

                if (坐标匹配) {
                    let left = parseInt(坐标匹配[1]);
                    let top = parseInt(坐标匹配[2]);
                    let right = parseInt(坐标匹配[3]);
                    let bottom = parseInt(坐标匹配[4]);

                    // 计算中心点
                    let x = Math.floor((left + right) / 2);
                    let y = Math.floor((top + bottom) / 2);

                    console.log(`解析到文章边界: [${left},${top}][${right},${bottom}], 中心点: (${x}, ${y})`);
                    return { x: x, y: y };
                }
            }
        }

        console.log("未在XML中找到文章坐标信息");
        return null;
    } catch (e) {
        console.error("解析XML查找文章坐标出错: " + e.message);
        return null;
    }
}

/**
 * 获取页面信息（自动分发）
 * 获取当前页面的标题、内容、点赞数、收藏数等信息
 * 
 * @returns {Object|null} - 页面信息，失败返回null
 */
function 获取页面信息() {
    try {
        // ROOT模式专用
        if (当前为ROOT模式()) {
            console.log("ROOT模式下获取页面信息");
            return 使用ROOT获取页面信息();
        } else {
            console.error("脚本已配置为仅支持ROOT模式");
            return null;
        }
    } catch (e) {
        console.error("获取页面信息出错: " + e.message);
        return null;
    }
}

/**
 * 使用ROOT模式获取页面信息
 * 
 * @returns {Object|null} - 页面信息
 */
function 使用ROOT获取页面信息() {
    try {
        // 获取当前窗口XML
        let pageXml = DeviceOperation.获取窗口XML();
        if (!pageXml) {
            console.error("获取页面XML失败");
            return null;
        }

        console.log("成功获取窗口XML，开始解析页面信息");

        // 初始化返回结果
        let 页面信息 = {
            标题: null,
            内容: null,
            用户名: null,
            点赞数: null,
            收藏数: null,
            评论数: null,
            已点赞: false,
            已收藏: false,
            是否视频: false,
            内容类型: "图文",
            最长文本列表: [], // 最长文本列表字段
            点赞按钮坐标: null,
            收藏按钮坐标: null
        };

        // 1. 解析用户名 - 简化版本
        console.log("开始尝试提取用户名...");

        // 方式1: 查找包含"作者"的content-desc属性
        let 作者匹配 = pageXml.match(/content-desc="作者([^"]+)"/);
        if (作者匹配) {
            页面信息.用户名 = 作者匹配[1].trim().replace(/^,/, ''); // 清理前面的逗号
            console.log(`找到用户名(方式1-作者前缀): ${页面信息.用户名}`);
        }

        // 方式2: 查找带有"作者"前缀但逗号分隔的情况
        if (!页面信息.用户名) {
            let 作者逗号匹配 = pageXml.match(/content-desc="作者,([^"]+)"/);
            if (作者逗号匹配) {
                页面信息.用户名 = 作者逗号匹配[1].trim();
                console.log(`找到用户名(方式2-作者逗号): ${页面信息.用户名}`);
            }
        }

        // 如果仍然没有找到用户名，输出警告
        if (!页面信息.用户名) {
            console.warn("⚠️ 未能提取到用户名");
        }

        // 2. 收集所有可能的文本内容
        console.log("开始提取文本内容...");
        let 页面文本内容 = [];

        // 2.1 提取所有text属性中的文本
        const 文本正则 = /text=['"]([^'"]+)['"]/g;
        let 文本匹配;
        while ((文本匹配 = 文本正则.exec(pageXml)) !== null) {
            let 文本 = 文本匹配[1].trim();
            if (文本.length > 5) { // 只收集长度大于5的文本
                // 过滤掉明显是功能按钮的文本
                if (!文本.match(/^(关注|点赞|收藏|评论|分享|举报|送礼物|说点什么)/) && 文本 !== "截屏分享至") {
                    页面文本内容.push(文本);
                    console.log(`[文本提取] 从text属性提取内容: ${文本.substring(0, 50)}${文本.length > 50 ? "..." : ""}`);
                }
            }
        }

        // 2.2 提取所有content-desc属性中的文本
        const 描述正则 = /content-desc=['"]([^'"]+)['"]/g;
        let 描述匹配;
        while ((描述匹配 = 描述正则.exec(pageXml)) !== null) {
            let 文本 = 描述匹配[1].trim();
            if (文本.length > 5 && !文本.startsWith("作者,") && !文本.startsWith("点赞") && !文本.startsWith("收藏") && !文本.startsWith("评论") && 文本 !== "截屏分享至") {
                页面文本内容.push(文本);
                console.log(`[文本提取] 从content-desc属性提取内容: ${文本.substring(0, 50)}${文本.length > 50 ? "..." : ""}`);
            }
        }

        // 3. 对文本内容进行处理和排序
        // 3.1 过滤掉重复内容
        let 唯一文本内容 = 页面文本内容.filter((value, index, self) => {
            return self.indexOf(value) === index;
        });

        // 3.2 按长度排序
        唯一文本内容.sort((a, b) => b.length - a.length);

        // 3.3 取最长的3段文本
        页面信息.最长文本列表 = 唯一文本内容.slice(0, 3);

        // 输出调试信息
        console.log(`[文本统计] 共提取到 ${唯一文本内容.length} 段文本内容`);
        for (let i = 0; i < 页面信息.最长文本列表.length; i++) {
            let 文本 = 页面信息.最长文本列表[i];
            let 显示文本 = 文本.length > 100 ? 文本.substring(0, 100) + "..." : 文本;
            console.log(`[长文本${i + 1}] (${文本.length}字): ${显示文本}`);
        }

        // 4. 为了兼容性，将最长文本设置为标题和内容
        if (页面信息.最长文本列表.length > 0) {
            页面信息.标题 = 页面信息.最长文本列表[0];

            if (页面信息.最长文本列表.length > 1) {
                页面信息.内容 = 页面信息.最长文本列表[1];
            }
        }



        // 5. 解析互动数据
        // 5.1 检查右侧互动区域
        let 互动区域XML = pageXml.match(/class="android\.widget\.LinearLayout"[^>]*bounds="\[927,\d+\]\[1080,\d+\]"[^>]*>([\s\S]*?)<\/node>/);

        if (互动区域XML) {
            // 解析点赞数据和坐标
            let 点赞匹配 = 互动区域XML[1].match(/content-desc="(已点赞|点赞)\s*(\d*)"[^>]*bounds='\[(\d+),(\d+)\]\[(\d+),(\d+)\]'/);
            if (!点赞匹配) {
                // 尝试更宽松的匹配：点赞和数字之间可能有空格
                点赞匹配 = 互动区域XML[1].match(/content-desc="(已点赞|点赞)\s+(\d+)"[^>]*bounds='\[(\d+),(\d+)\]\[(\d+),(\d+)\]'/);
            }
            if (!点赞匹配) {
                // 再次尝试：只匹配"点赞"关键词，数字可选
                点赞匹配 = 互动区域XML[1].match(/content-desc="(已点赞|点赞)(?:\s+(\d+))?"[^>]*bounds='\[(\d+),(\d+)\]\[(\d+),(\d+)\]'/);
            }

            if (点赞匹配) {
                页面信息.已点赞 = 点赞匹配[1] === "已点赞";
                页面信息.点赞数 = 点赞匹配[2] ? parseInt(点赞匹配[2]) : (页面信息.已点赞 ? 1 : 0);

                // 记录点赞按钮坐标 - 调整索引以适应新的正则表达式
                let 坐标索引偏移 = 点赞匹配[2] ? 0 : 1; // 如果没有数字，坐标索引需要调整
                let 点赞左 = parseInt(点赞匹配[3 + 坐标索引偏移]);
                let 点赞上 = parseInt(点赞匹配[4 + 坐标索引偏移]);
                let 点赞右 = parseInt(点赞匹配[5 + 坐标索引偏移]);
                let 点赞下 = parseInt(点赞匹配[6 + 坐标索引偏移]);

                页面信息.点赞按钮坐标 = {
                    左: 点赞左,
                    上: 点赞上,
                    右: 点赞右,
                    下: 点赞下,
                    中心X: Math.round((点赞左 + 点赞右) / 2),
                    中心Y: Math.round((点赞上 + 点赞下) / 2)
                };

                console.log(`✅ 从互动区域获取点赞按钮坐标: [${点赞左},${点赞上}][${点赞右},${点赞下}] 中心点: (${页面信息.点赞按钮坐标.中心X}, ${页面信息.点赞按钮坐标.中心Y})`);
            } else {
                console.log("❌ 互动区域未找到点赞按钮坐标");
            }

            // 解析收藏数据和坐标 - 修复正则表达式
            let 收藏匹配 = 互动区域XML[1].match(/content-desc="(已收藏|收藏)\s*(\d*)"[^>]*bounds='\[(\d+),(\d+)\]\[(\d+),(\d+)\]'/);
            if (!收藏匹配) {
                // 尝试更宽松的匹配：收藏和数字之间可能有空格
                收藏匹配 = 互动区域XML[1].match(/content-desc="(已收藏|收藏)\s+(\d+)"[^>]*bounds='\[(\d+),(\d+)\]\[(\d+),(\d+)\]'/);
            }
            if (!收藏匹配) {
                // 再次尝试：只匹配"收藏"关键词，数字可选
                收藏匹配 = 互动区域XML[1].match(/content-desc="(已收藏|收藏)(?:\s+(\d+))?"[^>]*bounds='\[(\d+),(\d+)\]\[(\d+),(\d+)\]'/);
            }

            if (收藏匹配) {
                页面信息.已收藏 = 收藏匹配[1] === "已收藏";
                页面信息.收藏数 = 收藏匹配[2] ? parseInt(收藏匹配[2]) : (页面信息.已收藏 ? 1 : 0);

                // 记录收藏按钮坐标 - 调整索引以适应新的正则表达式
                let 坐标索引偏移 = 收藏匹配[2] ? 0 : 1; // 如果没有数字，坐标索引需要调整
                let 收藏左 = parseInt(收藏匹配[3 + 坐标索引偏移]);
                let 收藏上 = parseInt(收藏匹配[4 + 坐标索引偏移]);
                let 收藏右 = parseInt(收藏匹配[5 + 坐标索引偏移]);
                let 收藏下 = parseInt(收藏匹配[6 + 坐标索引偏移]);

                页面信息.收藏按钮坐标 = {
                    左: 收藏左,
                    上: 收藏上,
                    右: 收藏右,
                    下: 收藏下,
                    中心X: Math.round((收藏左 + 收藏右) / 2),
                    中心Y: Math.round((收藏上 + 收藏下) / 2)
                };

                console.log(`✅ 从互动区域获取收藏按钮坐标: [${收藏左},${收藏上}][${收藏右},${收藏下}] 中心点: (${页面信息.收藏按钮坐标.中心X}, ${页面信息.收藏按钮坐标.中心Y})`);
            } else {
                console.log("❌ 互动区域未找到收藏按钮坐标");
            }

            // 解析评论数据
            let 评论匹配 = 互动区域XML[1].match(/content-desc="评论\s*(\d*)"/);
            if (评论匹配) {
                页面信息.评论数 = 评论匹配[1] ? parseInt(评论匹配[1]) : 0;
            }
        } else {
            // 5.2 如果没有找到右侧互动区域，尝试从底部区域查找
            console.log("右侧互动区域未找到数据，尝试底部互动区域");

            // 查找点赞数据和坐标 - 使用更简单直接的方法
            console.log("🔍 开始查找底部点赞按钮信息...");

            // 兼容所有点赞格式：使用更宽松的匹配策略

            // 先尝试找到点赞相关的content-desc，然后在同一个node中查找bounds
            let 点赞节点匹配 = pageXml.match(/<node[^>]*content-desc="(已点赞|点赞)(?:\s+)?(\d*)"[^>]*>/);
            if (点赞节点匹配) {
                let 完整节点 = 点赞节点匹配[0];
                console.log(`🔍 找到点赞节点: ${完整节点.substring(0, 100)}...`);

                // 从完整节点中提取bounds信息 - 修复引号格式
                let bounds匹配 = 完整节点.match(/bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);
                if (bounds匹配) {
                    let 状态 = 点赞节点匹配[1];
                    let 数量文本 = 点赞节点匹配[2];

                    页面信息.已点赞 = 状态 === "已点赞";
                    页面信息.点赞数 = 数量文本 ? parseInt(数量文本) : 0;

                    let 点赞左 = parseInt(bounds匹配[1]);
                    let 点赞上 = parseInt(bounds匹配[2]);
                    let 点赞右 = parseInt(bounds匹配[3]);
                    let 点赞下 = parseInt(bounds匹配[4]);

                    页面信息.点赞按钮坐标 = {
                        左: 点赞左, 上: 点赞上, 右: 点赞右, 下: 点赞下,
                        中心X: Math.round((点赞左 + 点赞右) / 2),
                        中心Y: Math.round((点赞上 + 点赞下) / 2)
                    };

                    console.log(`✅ 点赞信息: ${页面信息.已点赞 ? '已点赞' : '未点赞'}, 数量=${页面信息.点赞数}`);
                    console.log(`✅ 点赞按钮坐标: [${点赞左},${点赞上}][${点赞右},${点赞下}] 中心点: (${页面信息.点赞按钮坐标.中心X}, ${页面信息.点赞按钮坐标.中心Y})`);
                } else {
                    console.log("❌ 找到点赞content-desc但未找到bounds信息");
                }
            } else {
                console.log("❌ 底部区域未找到点赞按钮信息");
            }

            // 查找收藏数据和坐标 - 使用更简单直接的方法
            console.log("🔍 开始查找底部收藏按钮信息...");

            // 兼容所有收藏格式：使用更宽松的匹配策略

            // 先尝试找到收藏相关的content-desc，然后在同一个node中查找bounds
            let 收藏节点匹配 = pageXml.match(/<node[^>]*content-desc="(已收藏|收藏)(?:\s+)?(\d*)"[^>]*>/);
            if (收藏节点匹配) {
                let 完整节点 = 收藏节点匹配[0];
                console.log(`🔍 找到收藏节点: ${完整节点.substring(0, 100)}...`);

                // 从完整节点中提取bounds信息 - 修复引号格式
                let bounds匹配 = 完整节点.match(/bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);
                if (bounds匹配) {
                    let 状态 = 收藏节点匹配[1];
                    let 数量文本 = 收藏节点匹配[2];

                    页面信息.已收藏 = 状态 === "已收藏";
                    页面信息.收藏数 = 数量文本 ? parseInt(数量文本) : 0;

                    let 收藏左 = parseInt(bounds匹配[1]);
                    let 收藏上 = parseInt(bounds匹配[2]);
                    let 收藏右 = parseInt(bounds匹配[3]);
                    let 收藏下 = parseInt(bounds匹配[4]);

                    页面信息.收藏按钮坐标 = {
                        左: 收藏左, 上: 收藏上, 右: 收藏右, 下: 收藏下,
                        中心X: Math.round((收藏左 + 收藏右) / 2),
                        中心Y: Math.round((收藏上 + 收藏下) / 2)
                    };

                    console.log(`✅ 收藏信息: ${页面信息.已收藏 ? '已收藏' : '未收藏'}, 数量=${页面信息.收藏数}`);
                    console.log(`✅ 收藏按钮坐标: [${收藏左},${收藏上}][${收藏右},${收藏下}] 中心点: (${页面信息.收藏按钮坐标.中心X}, ${页面信息.收藏按钮坐标.中心Y})`);
                } else {
                    console.log("❌ 找到收藏content-desc但未找到bounds信息");
                }
            } else {
                console.log("❌ 底部区域未找到收藏按钮信息");
            }

            // 5. 解析评论数
            console.log("🔍 开始查找评论信息...");
            let 评论匹配 = pageXml.match(/content-desc="评论\s*(\d*)"/);
            if (评论匹配) {
                页面信息.评论数 = 评论匹配[1] ? parseInt(评论匹配[1]) : 0;
                console.log(`✅ 评论数: ${页面信息.评论数}`);
            } else {
                console.log("❌ 未找到评论信息");
            }
        }

        // 6. 判断是否为视频
        if (pageXml.includes('content-desc="暂停"') ||
            pageXml.includes('VideoSeekBar') ||
            pageXml.includes('已播放到') ||
            pageXml.includes('剩余时间') ||
            pageXml.match(/共\d+分\d+秒/)) {
            页面信息.是否视频 = true;
            页面信息.内容类型 = "视频";
            console.log("检测到视频内容");
        }

        console.log("🎉 页面信息解析完成");

        // 输出总结信息
        console.log("=" .repeat(50));
        console.log("📋 页面信息获取结果总结:");
        console.log("=" .repeat(50));

        console.log(`👤 用户名: ${页面信息.用户名 || '未获取到'}`);
        console.log(`📄 标题: ${页面信息.标题 ? 页面信息.标题.substring(0, 50) + (页面信息.标题.length > 50 ? '...' : '') : '未获取到'}`);
        console.log(`📝 内容: ${页面信息.内容 ? 页面信息.内容.substring(0, 50) + (页面信息.内容.length > 50 ? '...' : '') : '未获取到'}`);
        console.log(`📊 文本段数: ${页面信息.最长文本列表.length}`);

        console.log(`👍 点赞: ${页面信息.已点赞 ? '已点赞' : '未点赞'} (${页面信息.点赞数 || 0})`);
        console.log(`⭐ 收藏: ${页面信息.已收藏 ? '已收藏' : '未收藏'} (${页面信息.收藏数 || 0})`);
        console.log(`💬 评论: ${页面信息.评论数 || 0}`);

        if (页面信息.点赞按钮坐标) {
            let 坐标 = 页面信息.点赞按钮坐标;
            console.log(`📍 点赞按钮坐标: 中心点(${坐标.中心X}, ${坐标.中心Y})`);
        } else {
            console.log(`📍 点赞按钮坐标: 未获取到`);
        }

        if (页面信息.收藏按钮坐标) {
            let 坐标 = 页面信息.收藏按钮坐标;
            console.log(`📍 收藏按钮坐标: 中心点(${坐标.中心X}, ${坐标.中心Y})`);
        } else {
            console.log(`📍 收藏按钮坐标: 未获取到`);
        }

        console.log("=" .repeat(50));

        return 页面信息;
    } catch (e) {
        console.error("使用ROOT获取页面信息出错: " + e.message);
        return null;
    }
}

/**
 * 使用无障碍服务获取页面信息 (已禁用)
 *
 * @returns {Object|null} - 页面信息
 */
function 使用无障碍获取页面信息() {
    console.error("❌ 无障碍功能已被禁用");
    return null;
}

// 导出本地操作相关函数
module.exports = {
    // 主要操作函数
    开始小红书本地操作,
    开始小红书点赞操作: 开始小红书本地操作, // 别名，兼容main.js的调用

    // 配置相关
    读取配置文件,
    更新配置文件,
    读取URL文件,
    获取配置项,

    // 本地操作管理
    获取下一个URL,
    更新本地操作计数,
    保存本地操作计数,
    加载本地操作计数,
    重置本地操作计数,
    显示当前进度,
    记录操作日志,
    递增当前操作行号,
    处理链接操作完成,

    // 已操作链接管理
    读取已操作链接列表,
    保存已操作链接列表,
    添加已操作链接,
    检查链接是否已操作,

    // 飞行模式状态管理
    设置飞行模式执行标记,
    检查飞行模式执行标记,
    清除飞行模式执行标记,
    执行飞行模式操作,
    处理飞行模式逻辑,

    // 暂停控制
    创建暂停文件,
    删除暂停文件,

    // 换号管理
    检查是否需要换号,
    执行换号操作,
    重置换号计数,

    // 时间控制
    随机等待,
    等待,

    // 应用操作
    打开链接,
    打开小红书,
    返回主界面,
    执行首页下拉刷新,
    强制关闭小红书APP,
    检查账号异常提示,
    处理权限弹窗,
    点击首篇文章,

    // 页面信息获取（ROOT模式专用）
    获取页面信息,
    使用ROOT获取页面信息,
    获取互动元素,
    使用ROOT获取互动元素,
    比较页面信息,

    // 操作执行
    执行点赞,
    执行收藏,
    执行阅读操作,

    // 英文别名（兼容性）
    executeReading: 执行阅读操作,

    // 工具函数
    提取数字
};

// 添加模块加载完成日志
console.log("xiaohongshu.js 模块加载完成，所有函数已导出");

/**
 * 智能自动操作函数
 * 包含返回首页、点击首篇文章、比较文章信息等完整流程
 * 
 * @param {number} 最大操作次数 - 最大操作次数，0表示不限制
 * @returns {Object} - 操作结果
 */
function 智能自动操作(最大操作次数 = 0) {
    console.log("开始智能自动操作");

    // 获取操作信息
    let 操作信息 = 获取操作信息();
    if (!操作信息 || !操作信息.链接) {
        console.log("没有可操作的链接");
        return { success: false, message: "没有可操作的链接" };
    }

    let 链接 = 操作信息.链接;
    let 链接ID = 操作信息.链接ID;
    let 任务ID = 操作信息.任务ID;
    let 原始点赞数 = 操作信息.原始点赞数 || 0;
    let 原始收藏数 = 操作信息.原始收藏数 || 0;
    let 目标点赞数 = 操作信息.目标点赞数 || 0;
    let 目标收藏数 = 操作信息.目标收藏数 || 0;

    // 移除链接处理的详细日志，避免输出过多

    // 打开链接
    if (!打开链接(链接)) {
        console.log("打开链接失败，更新操作状态为失败");
        更新操作状态(链接ID, "failed", "both", 原始点赞数, 原始收藏数, "打开链接失败");
        return { success: false, message: "打开链接失败" };
    }

    // 等待页面加载
    等待(3000);

    // 获取目标页面信息
    console.log("获取目标页面信息...");
    let 目标内容 = 获取页面信息();
    if (!目标内容) {
        console.log("未获取到目标页面信息，可能小红书未打开或未进入正确页面");
        更新操作状态(链接ID, "failed", "both", 0, 0, "未获取到目标页面信息");
        return { success: false, message: "未获取到目标页面信息" };
    }

    console.log("成功获取目标页面信息：", 目标内容.标题 || 目标内容.内容);

    // 记录目标页面的点赞和收藏数
    let 互动元素 = 获取互动元素();
    let 操作前点赞数 = 互动元素 ? (互动元素.点赞 || 0) : 0;
    let 操作前收藏数 = 互动元素 ? (互动元素.收藏 || 0) : 0;
    console.log(`目标页面数据 - 点赞数: ${操作前点赞数}, 收藏数: ${操作前收藏数}`);

    // 返回首页
    console.log("返回首页...");
    if (!返回主界面()) {
        console.log("返回首页失败");
        更新操作状态(链接ID, "failed", "both", 操作前点赞数, 操作前收藏数, "返回首页失败");
        return { success: false, message: "返回首页失败" };
    }

    // 等待首页加载
    等待(2000);

    // 点击首页第一篇文章
    console.log("点击首页第一篇文章...");
    if (!点击首篇文章()) {
        console.log("点击首页第一篇文章失败");
        更新操作状态(链接ID, "failed", "both", 操作前点赞数, 操作前收藏数, "点击首页第一篇文章失败");
        return { success: false, message: "点击首页第一篇文章失败" };
    }

    // 等待文章页面加载
    等待(3000);

    // 获取首页第一篇文章的页面信息
    console.log("获取首页文章信息...");
    let 首页内容 = 获取页面信息();
    if (!首页内容) {
        console.log("未获取到首页文章信息");
        更新操作状态(链接ID, "failed", "both", 操作前点赞数, 操作前收藏数, "未获取到首页文章信息");
        return { success: false, message: "未获取到首页文章信息" };
    }

    console.log("成功获取首页文章信息：", 首页内容.标题 || 首页内容.内容);

    // 比对两次内容
    console.log("比对目标内容和首页内容...");
    if (!比较页面信息(目标内容, 首页内容)) {
        console.log("内容不匹配，不是同一篇文章");
        更新操作状态(链接ID, "failed", "both", 操作前点赞数, 操作前收藏数, "内容不匹配，不是同一篇文章");
        return { success: false, message: "内容不匹配，不是同一篇文章" };
    }

    console.log("内容匹配，确认是同一篇文章，准备执行点赞和收藏操作");

    // 计算需要增加的数量
    let 需要增加点赞数 = 目标点赞数 - (首页内容.点赞数 - 原始点赞数);
    let 需要增加收藏数 = 目标收藏数 - (首页内容.收藏数 - 原始收藏数);

    // 处理初始值为0的特殊情况
    if (原始点赞数 === 0) {
        需要增加点赞数 = 目标点赞数;
    }
    if (原始收藏数 === 0) {
        需要增加收藏数 = 目标收藏数;
    }

    console.log(`需要增加 - 点赞: ${需要增加点赞数}, 收藏: ${需要增加收藏数}`);

    // 检查是否已经达标
    let 点赞已达标 = 需要增加点赞数 <= 0;
    let 收藏已达标 = 需要增加收藏数 <= 0;

    if (点赞已达标 && 收藏已达标) {
        console.log("点赞和收藏均已达到目标数量，无需操作");
        更新操作状态(链接ID, "success", "both", 首页内容.点赞数, 首页内容.收藏数);
        返回主界面();
        return { success: true, message: "点赞和收藏均已达到目标数量，无需操作" };
    }

    // 根据是否达标决定是否执行操作
    let 点赞成功 = false;
    let 收藏成功 = false;

    // 执行点赞操作
    if (!点赞已达标 && 需要增加点赞数 > 0) {
        console.log(`开始执行点赞操作，需要增加 ${需要增加点赞数} 个点赞`);
        点赞成功 = 执行点赞(目标内容);
        if (点赞成功) {
            console.log("点赞操作成功");
        } else {
            console.log("点赞操作失败");
        }
    } else if (点赞已达标) {
        console.log("点赞已达标，跳过点赞操作");
        点赞成功 = true;
    }

    // 随机等待
    随机等待数字(1000, 2000);

    // 执行收藏操作
    if (!收藏已达标 && 需要增加收藏数 > 0) {
        console.log(`开始执行收藏操作，需要增加 ${需要增加收藏数} 个收藏`);
        收藏成功 = 执行收藏(目标内容);
        if (收藏成功) {
            console.log("收藏操作成功");
        } else {
            console.log("收藏操作失败");
        }
    } else if (收藏已达标) {
        console.log("收藏已达标，跳过收藏操作");
        收藏成功 = true;
    }

    // 确定操作类型
    let 操作类型 = "both";
    if (点赞已达标 && !收藏已达标) {
        操作类型 = "收藏";
    } else if (!点赞已达标 && 收藏已达标) {
        操作类型 = "点赞";
    }

    // 更新操作状态
    if (点赞成功 && 收藏成功) {
        console.log("所有操作成功完成");
        更新操作状态(链接ID, "success", 操作类型, 首页内容.点赞数, 首页内容.收藏数);
        返回主界面();
        return { success: true, message: "所有操作成功完成" };
    } else {
        console.log("部分操作失败");
        let 失败信息 = [];
        if (!点赞成功 && !点赞已达标) 失败信息.push("点赞失败");
        if (!收藏成功 && !收藏已达标) 失败信息.push("收藏失败");

        更新操作状态(链接ID, "failed", 操作类型, 首页内容.点赞数, 首页内容.收藏数, 失败信息.join(", "));
        返回主界面();
        return { success: false, message: 失败信息.join(", ") };
    }
}

// 配置读取已统一到main.js中的global.读取配置项

